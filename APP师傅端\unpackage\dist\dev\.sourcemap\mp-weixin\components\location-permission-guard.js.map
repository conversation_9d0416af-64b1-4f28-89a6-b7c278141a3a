{"version": 3, "sources": ["webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/components/location-permission-guard.vue?3864", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/components/location-permission-guard.vue?bb92", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/components/location-permission-guard.vue?2d2b", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/components/location-permission-guard.vue?eac2", "uni-app:///components/location-permission-guard.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/components/location-permission-guard.vue?b209", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/components/location-permission-guard.vue?6209"], "names": ["name", "props", "autoCheck", "type", "default", "message", "required", "showCancel", "data", "isChecking", "hasPermission", "showPermissionDenied", "computed", "permissionMessage", "mounted", "methods", "checkPermission", "locationPermissionManager", "status", "granted", "console", "handleOpenSettings", "setTimeout", "uni", "title", "icon", "handleCancel", "recheckPermission", "requestPermission"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkJ;AAClJ;AAC6E;AACL;AACsC;;;AAG9G;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,+FAAM;AACR,EAAE,gHAAM;AACR,EAAE,yHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAw2B,CAAgB,w3BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC4B53B;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;IACA;IACA;IACAE;MACAH;MACAC;IACA;IACA;IACAG;MACAJ;MACAC;IACA;EACA;EAEAI;IACA;MACAC;MACAC;MACAC;IACA;EACA;EAEAC;IACAC;MACA;IACA;EACA;EAEAC;IACA;MACA;IACA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBACA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEAD;cAAA;gBAAAE;gBAEA;kBACA;kBACA;gBACA;kBACA;kBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAC;gBACA;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACAJ;kBACA;;kBAEA;kBACAK;oBACA;kBACA;gBACA;kBACAF;kBACAG;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACA;QACA;QACAH;UACAC;UACAC;QACA;QACA;MACA;MAEA;MACA;IACA;IAEA;IACAE;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzJA;AAAA;AAAA;AAAA;AAA+mD,CAAgB,mkDAAG,EAAC,C;;;;;;;;;;;ACAnoD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/location-permission-guard.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./location-permission-guard.vue?vue&type=template&id=fa26c7d8&scoped=true&\"\nvar renderjs\nimport script from \"./location-permission-guard.vue?vue&type=script&lang=js&\"\nexport * from \"./location-permission-guard.vue?vue&type=script&lang=js&\"\nimport style0 from \"./location-permission-guard.vue?vue&type=style&index=0&id=fa26c7d8&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fa26c7d8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/location-permission-guard.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-permission-guard.vue?vue&type=template&id=fa26c7d8&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-permission-guard.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-permission-guard.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"location-guard\">\n    <!-- 权限检查中的加载状态 -->\n    <view class=\"loading-overlay\" v-if=\"isChecking\">\n      <view class=\"loading-content\">\n        <text class=\"loading-text\">检查定位权限中...</text>\n      </view>\n    </view>\n\n    <!-- 权限被拒绝时的提示界面 -->\n    <view class=\"permission-denied\" v-if=\"showPermissionDenied\">\n      <view class=\"denied-content\">\n        <view class=\"denied-icon\">📍</view>\n        <text class=\"denied-title\">需要定位权限</text>\n        <text class=\"denied-message\">{{ permissionMessage }}</text>\n        <view class=\"denied-buttons\">\n          <button class=\"btn-secondary\" @click=\"handleCancel\">暂不开启</button>\n          <button class=\"btn-primary\" @click=\"handleOpenSettings\">去设置</button>\n        </view>\n      </view>\n    </view>\n\n    <!-- 有权限时显示的内容插槽 -->\n    <slot v-if=\"hasPermission && !isChecking\"></slot>\n  </view>\n</template>\n\n<script>\nimport locationPermissionManager from '@/utils/location-permission.js';\n\nexport default {\n  name: 'LocationPermissionGuard',\n  props: {\n    // 是否自动检查权限\n    autoCheck: {\n      type: Boolean,\n      default: true\n    },\n    // 权限提示消息\n    message: {\n      type: String,\n      default: '为了更好地为您提供服务，需要获取您的位置信息。'\n    },\n    // 是否强制要求权限（如果为true，用户拒绝后不显示内容）\n    required: {\n      type: Boolean,\n      default: false\n    },\n    // 是否显示取消按钮\n    showCancel: {\n      type: Boolean,\n      default: true\n    }\n  },\n  \n  data() {\n    return {\n      isChecking: false,\n      hasPermission: false,\n      showPermissionDenied: false\n    };\n  },\n\n  computed: {\n    permissionMessage() {\n      return this.message;\n    }\n  },\n\n  mounted() {\n    if (this.autoCheck) {\n      this.checkPermission();\n    }\n  },\n\n  methods: {\n    // 检查定位权限\n    async checkPermission() {\n      this.isChecking = true;\n      this.showPermissionDenied = false;\n\n      try {\n        const status = await locationPermissionManager.checkPermissionStatus();\n        this.hasPermission = status.hasPermission;\n\n        if (!this.hasPermission) {\n          // 先尝试请求权限\n          const granted = await locationPermissionManager.requestPermission();\n          \n          if (granted) {\n            this.hasPermission = true;\n            this.$emit('permission-granted');\n          } else {\n            // 权限被拒绝，显示引导界面\n            this.showPermissionDenied = true;\n            this.$emit('permission-denied');\n          }\n        } else {\n          this.$emit('permission-granted');\n        }\n      } catch (error) {\n        console.error('权限检查失败:', error);\n        this.showPermissionDenied = true;\n        this.$emit('permission-error', error);\n      } finally {\n        this.isChecking = false;\n      }\n    },\n\n    // 处理打开设置\n    async handleOpenSettings() {\n      try {\n        locationPermissionManager.openSystemSettings();\n        this.$emit('open-settings');\n        \n        // 可选：延迟后重新检查权限\n        setTimeout(() => {\n          this.recheckPermission();\n        }, 3000);\n      } catch (error) {\n        console.error('打开设置失败:', error);\n        uni.showToast({\n          title: '打开设置失败',\n          icon: 'none'\n        });\n      }\n    },\n\n    // 处理取消\n    handleCancel() {\n      if (this.required) {\n        // 如果是必需的权限，不允许取消\n        uni.showToast({\n          title: '该功能需要定位权限',\n          icon: 'none'\n        });\n        return;\n      }\n\n      this.showPermissionDenied = false;\n      this.$emit('permission-cancelled');\n    },\n\n    // 重新检查权限\n    async recheckPermission() {\n      await this.checkPermission();\n    },\n\n    // 手动触发权限检查（供外部调用）\n    async requestPermission() {\n      await this.checkPermission();\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.location-guard {\n  position: relative;\n  width: 100%;\n  height: 100%;\n}\n\n.loading-overlay {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: rgba(255, 255, 255, 0.9);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9999;\n\n  .loading-content {\n    text-align: center;\n    \n    .loading-text {\n      font-size: 28rpx;\n      color: #666;\n    }\n  }\n}\n\n.permission-denied {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: #fff;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  z-index: 9998;\n\n  .denied-content {\n    text-align: center;\n    padding: 60rpx 40rpx;\n    max-width: 600rpx;\n\n    .denied-icon {\n      font-size: 120rpx;\n      margin-bottom: 40rpx;\n    }\n\n    .denied-title {\n      display: block;\n      font-size: 36rpx;\n      font-weight: bold;\n      color: #333;\n      margin-bottom: 20rpx;\n    }\n\n    .denied-message {\n      display: block;\n      font-size: 28rpx;\n      color: #666;\n      line-height: 1.6;\n      margin-bottom: 60rpx;\n    }\n\n    .denied-buttons {\n      display: flex;\n      gap: 30rpx;\n      justify-content: center;\n\n      button {\n        flex: 1;\n        height: 88rpx;\n        border-radius: 12rpx;\n        font-size: 30rpx;\n        border: none;\n        max-width: 200rpx;\n\n        &.btn-primary {\n          background-color: #1890ff;\n          color: #fff;\n        }\n\n        &.btn-secondary {\n          background-color: #f5f5f5;\n          color: #666;\n        }\n      }\n    }\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-permission-guard.vue?vue&type=style&index=0&id=fa26c7d8&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-permission-guard.vue?vue&type=style&index=0&id=fa26c7d8&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755745105830\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}