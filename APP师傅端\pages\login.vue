<template>
	<view class="login-container">
		<!-- Background Decoration -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<!-- Header -->
		<view class="header">
			<view class="back-btn" @click="goBack">
				<u-icon name="arrow-left" color="#3b82f6" size="20"></u-icon>
			</view>
		</view>

		<!-- Logo Section -->
		<view class="logo-section">
			<view class="logo-wrapper">
				<image class="logo" src="/static/images/logo-index.jpg" mode="aspectFit"></image>
			</view>
			<view class="app-name">今师傅</view>
			<view class="welcome-text">{{ getWelcomeText() }}</view>
		</view>

		<!-- Main Card -->
		<view class="main-card">
			<!-- Mode Title -->
			<!-- <view class="mode-title">
				{{ currentMode === 'login' ? '欢迎回来' : currentMode === 'register' ? '创建账号' : '重置密码' }}
			</view> -->

			<!-- Login Form -->
			<view v-if="currentMode === 'login'">
				<!-- Tab Switcher -->
				<view class="tab-switcher">
					<view class="tab-item" :class="{ active: loginType === 'password' }"
						@click="switchLoginType('password')">
						<u-icon name="lock" size="16"
							:color="loginType === 'password' ? '#3b82f6' : '#94a3b8'"></u-icon>
						<text>密码登录</text>
					</view>
					<view class="tab-item" :class="{ active: loginType === 'sms' }" @click="switchLoginType('sms')">
						<u-icon name="chat" size="16" :color="loginType === 'sms' ? '#3b82f6' : '#94a3b8'"></u-icon>
						<text>验证码登录</text>
					</view>
				</view>

				<!-- Password Login -->
				<view v-if="loginType === 'password'" class="form-content">
					<view class="input-group">
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="phone" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入手机号" v-model="loginForm.phone"
								maxlength="11" />
						</view>
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="lock" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请输入密码"
								v-model="loginForm.password" />
							<view class="action-icon" @click="togglePassword">
								<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#94a3b8" size="18"></u-icon>
							</view>
						</view>
					</view>
					<view class="login-links">
						<view class="forgot-link" @click="switchMode('forgot')">忘记密码？</view>
						<view class="register-link" @click="switchMode('register')">
							还没有账号？<text class="link-highlight">立即注册</text>
						</view>
					</view>
				</view>

				<!-- SMS Login -->
				<view v-if="loginType === 'sms'" class="form-content">
					<view class="input-group">
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="phone" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入手机号" v-model="smsForm.phone"
								maxlength="11" />
						</view>
						<view class="input-item">
							<view class="input-icon">
								<u-icon name="chat" color="#3b82f6" size="18"></u-icon>
							</view>
							<input class="input-field" type="number" placeholder="请输入验证码" v-model="smsForm.code"
								maxlength="6" />
							<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
								{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- Register Form -->
			<view v-if="currentMode === 'register'" class="form-content">
				<!-- 注册步骤指示器 -->

				<!-- 注册提示 -->


				<view class="input-group">
					<!-- 手机号输入 -->
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="phone" color="#3b82f6" size="18"></u-icon>
						</view>
						<view class="input-content">
							<view class="input-label">手机号</view>
							<input class="input-field" type="number" placeholder="请输入11位手机号"
								v-model="registerForm.phone" maxlength="11" />
						</view>
						<view class="input-status" v-if="registerForm.phone && validatePhone(registerForm.phone)">
							<u-icon name="checkmark-circle" color="#52c41a" size="18"></u-icon>
						</view>
					</view>

					<!-- 密码输入 -->
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="lock" color="#3b82f6" size="18"></u-icon>
						</view>
						<view class="input-content">
							<view class="input-label">设置密码</view>
							<input class="input-field" :type="showPassword ? 'text' : 'password'"
								placeholder="请设置6-20位密码" v-model="registerForm.password" />
						</view>
						<view class="action-icon" @click="togglePassword">
							<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#94a3b8" size="18"></u-icon>
						</view>
					</view>

					<!-- 密码强度指示器 -->
					<view class="password-strength" v-if="registerForm.password">
						<view class="strength-label">密码强度：</view>
						<view class="strength-bar">
							<view class="strength-item" :class="{ active: getPasswordStrength() >= 1 }"></view>
							<view class="strength-item" :class="{ active: getPasswordStrength() >= 2 }"></view>
							<view class="strength-item" :class="{ active: getPasswordStrength() >= 3 }"></view>
						</view>
						<view class="strength-text">{{ getPasswordStrengthText() }}</view>
					</view>

					<!-- 验证码输入 -->
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="chat" color="#3b82f6" size="18"></u-icon>
						</view>
						<view class="input-content">
							<view class="input-label">短信验证码</view>
							<input class="input-field" type="number" placeholder="请输入6位验证码"
								v-model="registerForm.shortCode" maxlength="6" />
						</view>
						<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
							{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
						</view>
					</view>

					<!-- 邀请码输入 -->
					<view class="input-item optional">
						<view class="input-icon">
							<u-icon name="gift" color="#3b82f6" size="18"></u-icon>
						</view>
						<view class="input-content">
							<view class="input-label">
								邀请码
								<text class="optional-tag">选填</text>
							</view>
							<input class="input-field" type="text" placeholder="填写邀请码可获得奖励"
								v-model="registerForm.pidInviteCode" />
						</view>
						<view class="input-benefit" v-if="registerForm.pidInviteCode">
							<u-icon name="gift" color="#ff9500" size="16"></u-icon>
						</view>
					</view>
				</view>


			</view>

			<!-- Forgot Password Form -->
			<view v-if="currentMode === 'forgot'" class="form-content">
				<view class="input-group">
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="phone" color="#3b82f6" size="18"></u-icon>
						</view>
						<input class="input-field" type="number" placeholder="请输入手机号" v-model="forgotForm.phone"
							maxlength="11" />
					</view>
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="lock" color="#3b82f6" size="18"></u-icon>
						</view>
						<input class="input-field" :type="showPassword ? 'text' : 'password'" placeholder="请输入新密码"
							v-model="forgotForm.newPassword" />
						<view class="action-icon" @click="togglePassword">
							<u-icon :name="showPassword ? 'eye' : 'eye-off'" color="#94a3b8" size="18"></u-icon>
						</view>
					</view>
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="lock" color="#3b82f6" size="18"></u-icon>
						</view>
						<input class="input-field" :type="showConfirmPassword ? 'text' : 'password'"
							placeholder="请确认新密码" v-model="forgotForm.confirmPassword" />
						<view class="action-icon" @click="toggleConfirmPassword">
							<u-icon :name="showConfirmPassword ? 'eye' : 'eye-off'" color="#94a3b8" size="18"></u-icon>
						</view>
					</view>
					<view class="input-item">
						<view class="input-icon">
							<u-icon name="chat" color="#3b82f6" size="18"></u-icon>
						</view>
						<input class="input-field" type="number" placeholder="请输入验证码" v-model="forgotForm.shortCode"
							maxlength="6" />
						<view class="sms-btn" @click="sendSmsCode" :class="{ disabled: smsCountdown > 0 }">
							{{ smsCountdown > 0 ? `${smsCountdown}s` : '获取验证码' }}
						</view>
					</view>
				</view>
			</view>

			<!-- Agreement Section -->
			<view class="agreement-section">
				<view class="checkbox-container" @click="toggleAgreement">
					<view class="checkbox" :class="{ checked: agreedToTerms }">
						<u-icon v-if="agreedToTerms" name="checkmark" color="#fff" size="12"></u-icon>
					</view>
					<view class="agreement-text">
						我已阅读并同意
						<text class="link" @click.stop="navigateToAgreement('service')">《服务协议》</text>
						和
						<text class="link" @click.stop="navigateToAgreement('privacy')">《隐私政策》</text>
					</view>
				</view>
			</view>

			<!-- Action Button -->
			<view class="action-button" :class="{ disabled: isLoading }" @click="handleSubmit">
				<view v-if="isLoading" class="loading-icon">

				</view>
				<text>{{ isLoading ? '处理中...' : getButtonText() }}</text>
			</view>

			<!-- WeChat Login Button -->
			<view class="wechat-login-button" @click="handleWechatLogin" v-if="currentMode === 'login'"
				:class="{ disabled: isWechatLoading }">
				<view v-if="isWechatLoading" class="loading-icon">

				</view>
				<!-- <view class="wechat-icon">

				</view> -->
				<text>{{ isWechatLoading ? '微信登录中...' : '微信登录' }}</text>
			</view>

			<!-- Switch Mode Links -->
			<view class="switch-links">
				<view v-if="currentMode === 'register'" class="link-text" @click="switchMode('login')">
					已有账号？<text class="link-highlight">立即登录</text>
				</view>
				<view v-if="currentMode === 'forgot'" class="link-text" @click="switchMode('login')">
					<text class="link-highlight">返回登录</text>
				</view>
			</view>
		</view>

		<!-- Bottom Decoration -->
		<view class="bottom-decoration">
			<view class="wave"></view>
		</view>
	</view>
</template>

<script>
import { mapMutations } from 'vuex';
import { md5 } from '@/utils/md5.js';
import { extractTokenFromHeaders } from '@/utils/cookieParser.js';

export default {
	data() {
		return {
			currentMode: 'login', // login, register, forgot
			loginType: 'password', // password, sms
			showPassword: false,
			showConfirmPassword: false,
			agreedToTerms: false,
			isLoading: false,
			isWechatLoading: false, // 微信登录独立的加载状态
			smsCountdown: 0,
			smsTimer: null,
			registerID: '',
			// Login forms
			loginForm: {
				phone: '13155308198',
				password: '123456789.'
			},
			smsForm: {
				phone: '',
				code: ''
			},
			registerForm: {
				phone: '',
				password: '',
				shortCode: '',
				pidInviteCode: ''
			},
			forgotForm: {
				phone: '',
				newPassword: '',
				confirmPassword: '',
				shortCode: ''
			}
		};
	},
	computed: {
		canSubmit() {
			if (!this.agreedToTerms) return false;

			if (this.currentMode === 'login') {
				if (this.loginType === 'password') {
					return this.loginForm.phone && this.loginForm.password;
				} else {
					return this.smsForm.phone && this.smsForm.code;
				}
			} else if (this.currentMode === 'register') {
				return this.registerForm.phone && this.registerForm.password && this.registerForm.shortCode;
			} else if (this.currentMode === 'forgot') {
				return this.forgotForm.phone && this.forgotForm.newPassword &&
					this.forgotForm.confirmPassword && this.forgotForm.shortCode;
			}
			return false;
		}
	},
	onLoad(options) {
		this.registerID = uni.getStorageSync("registerID")
		// 获取邀请码
		if (options.inviteCode) {
			this.registerForm.pidInviteCode = options.inviteCode;
		}
	},
	methods: {
		...mapMutations('user', ['updateUserItem']),

		goBack() {
			uni.navigateBack();
		},

		// 检查当前平台
		getCurrentPlatform() {
			// #ifdef APP-PLUS
			return 'app-plus';
			// #endif
			// #ifdef MP-WEIXIN
			return 'mp-weixin';
			// #endif
			// #ifdef H5
			return 'h5';
			// #endif
			return 'unknown';
		},

		// 获取平台类型数值
		getPlatformType() {
			// #ifdef APP-PLUS
			return 1; // APP
			// #endif

			// #ifdef MP-WEIXIN
			return 0; // 微信小程序
			// #endif

			// #ifdef H5
			return 0; // H5当作小程序处理
			// #endif

			// 默认返回小程序
			return 0;
		},

		getWelcomeText() {
			switch (this.currentMode) {
				case 'login': return '欢迎回来，请登录您的账号';
				case 'register': return '创建新账号，开始您的服务之旅';
				case 'forgot': return '重置密码，找回您的账号';
				default: return '';
			}
		},

		getButtonText() {
			switch (this.currentMode) {
				case 'login': return '登录';
				case 'register': return '注册';
				case 'forgot': return '重置密码';
				default: return '';
			}
		},

		switchMode(mode) {
			this.currentMode = mode;
			this.clearForms();
		},

		switchLoginType(type) {
			this.loginType = type;
		},

		togglePassword() {
			this.showPassword = !this.showPassword;
		},

		toggleConfirmPassword() {
			this.showConfirmPassword = !this.showConfirmPassword;
		},

		toggleAgreement() {
			this.agreedToTerms = !this.agreedToTerms;
		},

		clearForms() {
			this.loginForm = { phone: '', password: '' };
			this.smsForm = { phone: '', code: '' };
			this.registerForm = { phone: '', password: '', shortCode: '', pidInviteCode: '' };
			this.forgotForm = { phone: '', newPassword: '', confirmPassword: '', shortCode: '' };
		},

		navigateToAgreement(type) {
			console.log('=== 跳转协议页面 ===', type)

			let url = '../user/configuser';
			if (type === 'service') {
				url += '?type=service';
			} else if (type === 'privacy') {
				url += '?type=privacy';
			}

			console.log('跳转URL:', url)

			uni.navigateTo({
				url,
				success: () => {
					console.log('跳转协议页面成功')
				},
				fail: (error) => {
					console.error('跳转协议页面失败:', error)

					// 备用方案：使用webview打开外部链接
					const fallbackUrl = type === 'service'
						? 'https://m.zskj.asia/static/protocol.htm'
						: 'https://zskj.asia/privacy.html'

					const webviewUrl = `../user/webview?url=${encodeURIComponent(fallbackUrl)}&title=${encodeURIComponent(type === 'service' ? '服务协议' : '隐私政策')}`

					console.log('使用备用方案，webview URL:', webviewUrl)

					uni.navigateTo({
						url: webviewUrl,
						fail: (webviewError) => {
							console.error('webview跳转也失败:', webviewError)
							uni.showToast({
								title: '页面加载失败，请重试',
								icon: 'none'
							})
						}
					})
				}
			});
		},

		// 验证手机号
		validatePhone(phone) {
			const phoneReg = /^1[3-9]\d{9}$/;
			return phoneReg.test(phone);
		},

		// 发送短信验证码
		async sendSmsCode() {
			if (this.smsCountdown > 0) return;

			let phone = '';
			if (this.currentMode === 'login' && this.loginType === 'sms') {
				phone = this.smsForm.phone;
			} else if (this.currentMode === 'register') {
				phone = this.registerForm.phone;
			} else if (this.currentMode === 'forgot') {
				phone = this.forgotForm.phone;
			}

			if (!this.validatePhone(phone)) {
				return this.showToast('请输入正确的手机号');
			}

			try {
				// 调用发送验证码接口
				const response = await this.$api.base.sendSmsCode({ phone });

				if (response.code === '200') {
					this.showToast('验证码发送成功', 'success');
					this.startCountdown();
				} else {
					this.showToast(response.msg || '验证码发送失败，请重试');
				}
			} catch (error) {
				console.error('发送验证码失败:', error);
				this.showToast('验证码发送失败，请重试');
			}
		},

		// 开始倒计时
		startCountdown() {
			this.smsCountdown = 60;
			this.smsTimer = setInterval(() => {
				this.smsCountdown--;
				if (this.smsCountdown <= 0) {
					clearInterval(this.smsTimer);
					this.smsTimer = null;
				}
			}, 1000);
		},

		// 主要提交处理
		async handleSubmit() {
			if (this.isLoading) return;

			// 检查是否同意协议
			if (!this.agreedToTerms) {
				this.showToast('请勾选我已阅读并同意服务协议和隐私政策');
				return;
			}

			// 检查表单数据
			if (!this.canSubmit) {
				// 根据当前模式提供具体的提示
				if (this.currentMode === 'login') {
					if (this.loginType === 'password') {
						if (!this.loginForm.phone) {
							this.showToast('请输入手机号');
						} else if (!this.loginForm.password) {
							this.showToast('请输入密码');
						}
					} else {
						if (!this.smsForm.phone) {
							this.showToast('请输入手机号');
						} else if (!this.smsForm.code) {
							this.showToast('请输入验证码');
						}
					}
				} else if (this.currentMode === 'register') {
					if (!this.registerForm.phone) {
						this.showToast('请输入手机号');
					} else if (!this.registerForm.password) {
						this.showToast('请设置密码');
					} else if (!this.registerForm.shortCode) {
						this.showToast('请输入验证码');
					}
				} else if (this.currentMode === 'forgot') {
					if (!this.forgotForm.phone) {
						this.showToast('请输入手机号');
					} else if (!this.forgotForm.newPassword) {
						this.showToast('请输入新密码');
					} else if (!this.forgotForm.confirmPassword) {
						this.showToast('请确认新密码');
					} else if (!this.forgotForm.shortCode) {
						this.showToast('请输入验证码');
					}
				}
				return;
			}

			this.isLoading = true;

			try {
				if (this.currentMode === 'login') {
					if (this.loginType === 'password') {
						await this.handlePasswordLogin();
					} else {
						await this.handleSmsLogin();
					}
				} else if (this.currentMode === 'register') {
					await this.handleRegister();
				} else if (this.currentMode === 'forgot') {
					await this.handleForgotPassword();
				}
			} catch (error) {
				console.error('操作失败:', error);
				this.showToast(error.message || '操作失败，请重试');
			} finally {
				this.isLoading = false;
			}
		},

		// 账号密码登录
		async handlePasswordLogin() {
			const { phone, password } = this.loginForm;

			if (!this.validatePhone(phone)) {
				throw new Error('请输入正确的手机号');
			}

			if (!password) {
				throw new Error('请输入密码');
			}

			// 获取平台类型
			const isapp = this.getPlatformType();
			console.log('当前登录平台类型 isapp:', isapp);

			const params = {
				phone,
				password: md5(password),
				platform: 1, // 师傅端
				registrationId: this.registerID, // 极光推送id，暂时为空
				isapp: isapp // 添加平台类型参数
			};

			// 使用API方法
			const response = await this.$api.base.appLoginByPass(params);
			await this.handleLoginSuccess(response);
		},

		// 短信验证码登录
		async handleSmsLogin() {
			const { phone, code } = this.smsForm;

			if (!this.validatePhone(phone)) {
				throw new Error('请输入正确的手机号');
			}

			if (!code) {
				throw new Error('请输入验证码');
			}

			// 获取平台类型
			const isapp = this.getPlatformType();
			console.log('当前登录平台类型 isapp:', isapp);

			const params = {
				phone,
				code,
				platform: 1, // 师傅端
				registrationId: this.registerID, // 极光推送id，暂时为空
				isapp: isapp // 添加平台类型参数
			};

			// 使用API方法
			const response = await this.$api.base.appLoginByCode(params);
			await this.handleLoginSuccess(response);
		},

		// 注册
		async handleRegister() {
			const { phone, password, shortCode, pidInviteCode } = this.registerForm;

			if (!this.validatePhone(phone)) {
				throw new Error('请输入正确的手机号');
			}

			if (!password) {
				throw new Error('请输入密码');
			}

			if (password.length < 6) {
				throw new Error('密码长度不能少于6位');
			}

			if (!shortCode) {
				throw new Error('请输入验证码');
			}

			// 获取平台类型
			const isapp = this.getPlatformType();
			console.log('当前注册平台类型 isapp:', isapp);

			const params = {
				phone,
				password: md5(password),
				shortCode,
				pidInviteCode: pidInviteCode || '',
				isapp: isapp // 添加平台类型参数
			};

			// 使用API方法
			const response = await this.$api.base.appRegister(params);

			if (response.code === '200') {
				this.showToast('注册成功', 'success');

				// 检查注册响应中是否包含token（有些后端会在注册时直接返回token）
				const token = this.extractTokenFromHeaders(response.header);

				if (token && response.data) {
					// 如果注册时直接返回了token和用户信息，直接处理登录成功
					console.log('注册时直接返回了token，跳过自动登录步骤');
					await this.handleLoginSuccess(response);
				} else {
					// 否则进行自动登录
					console.log('注册时未返回token，进行自动登录');
					this.showToast('注册成功，正在自动登录...', 'success');
					await this.autoLoginAfterRegister(phone, password);
				}
			} else {
				throw new Error(response.msg || '注册失败');
			}
		},

		// 提取token的辅助方法
		extractTokenFromHeaders(headers) {
			return extractTokenFromHeaders(headers);
		},

		// 注册成功后自动登录
		async autoLoginAfterRegister(phone, password) {
			try {
				// 获取平台类型
				const isapp = this.getPlatformType();
				console.log('注册后自动登录平台类型 isapp:', isapp);

				const loginParams = {
					phone,
					password: md5(password),
					platform: 1, // 师傅端
					registrationId: this.registerID, // 极光推送id，暂时为空
					isapp: isapp // 添加平台类型参数
				};

				// 调用登录接口
				const loginResponse = await this.$api.base.appLoginByPass(loginParams);

				// 处理登录成功
				await this.handleLoginSuccess(loginResponse);

				this.showToast('登录成功', 'success');

				// 确保所有数据保存完成后再跳转
				await this.$nextTick();

				// 验证数据是否正确保存
				console.log('跳转前验证数据:', {
					token: uni.getStorageSync('token'),
					vuexToken: this.$store.state.user.autograph,
					userInfo: this.$store.state.user.userInfo
				});

				// 跳转到个人页面
				setTimeout(() => {
					uni.reLaunch({
						url: '/pages/mine'
					});
				}, 800); // 减少延迟时间

			} catch (error) {
				console.error('自动登录失败:', error);
				this.showToast('注册成功，请手动登录');

				// 自动登录失败，切换到登录模式并填充表单
				setTimeout(() => {
					this.loginForm.phone = phone;
					this.loginForm.password = '';
					this.currentMode = 'login';
					this.loginType = 'password';
				}, 1500);
			}
		},

		// 忘记密码
		async handleForgotPassword() {
			const { phone, newPassword, confirmPassword, shortCode } = this.forgotForm;

			if (!this.validatePhone(phone)) {
				throw new Error('请输入正确的手机号');
			}

			if (!newPassword) {
				throw new Error('请输入新密码');
			}

			if (newPassword.length < 6) {
				throw new Error('密码长度不能少于6位');
			}

			if (newPassword !== confirmPassword) {
				throw new Error('两次输入的密码不一致');
			}

			if (!shortCode) {
				throw new Error('请输入验证码');
			}

			// 获取平台类型
			const isapp = this.getPlatformType();
			console.log('当前重置密码平台类型 isapp:', isapp);

			const params = {
				phone,
				newPassword: md5(newPassword),
				confirmPassword: md5(confirmPassword),
				shortCode,
				isapp: isapp // 添加平台类型参数
			};

			// 使用API方法
			const response = await this.$api.base.appForgetPwd(params);

			if (response.code === '200') {
				this.showToast('密码重置成功', 'success');
			} else {
				throw new Error(response.msg || '密码重置失败');
			}

			// 重置成功后跳转到登录
			setTimeout(() => {
				this.loginForm.phone = phone;
				this.loginForm.password = '';
				this.currentMode = 'login';
				this.loginType = 'password';
			}, 1500);
		},

		// 处理登录成功
		async handleLoginSuccess(response) {
			console.log('登录响应:', response);

			if (!response || response.code !== '200') {
				throw new Error(response?.msg || '登录失败');
			}

			// 从响应头中提取token
			const token = extractTokenFromHeaders(response.header);

			if (!token) {
				console.error('未找到token，响应头:', response.header);
				throw new Error('登录失败，未获取到token');
			}

			// 保存token和用户信息
			uni.setStorageSync('token', token);

			// 确保 Vuex 更新完成
			await this.$nextTick();
			this.updateUserItem({
				key: 'autograph',
				val: token
			});

			// 再次等待确保状态更新完成
			await this.$nextTick();

			console.log('Token 保存完成:', {
				storage: uni.getStorageSync('token'),
				vuex: this.$store.state.user.autograph
			});

			// 保存用户信息
			const userInfo = response.data;
			const userInfoFormatted = {
				phone: userInfo.phone || '',
				avatarUrl: userInfo.avatarUrl || '/static/mine/default_user.png',
				nickName: userInfo.nickName || '用户',
				userId: userInfo.id || '',
				createTime: userInfo.createTime || '',
				pid: userInfo.pid || '',
				inviteCode: userInfo.inviteCode || ''
			};

			await this.$nextTick();
			this.updateUserItem({
				key: 'userInfo',
				val: userInfoFormatted
			});

			// 等待 Vuex 更新完成
			await this.$nextTick();

			// 保存到本地存储
			this.saveUserInfoToStorage(userInfoFormatted);

			// 登录成功后获取师傅信息
			try {
				await this.fetchShifuInfoAfterLogin(userInfoFormatted);
			} catch (error) {
				console.error('获取师傅信息失败:', error);
				// 即使获取师傅信息失败，也继续登录流程
			}

			this.showToast('登录成功', 'success');

			// 确保所有数据保存完成后再跳转
			await this.$nextTick();

			// 跳转到个人页面
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/mine'
				});
			}, 800); // 减少延迟时间
		},

		// 登录成功后获取师傅信息
		async fetchShifuInfoAfterLogin(userInfo) {
			try {
				// 1. 检查师傅状态
				const shifuStatusRes = await this.$api.shifu.getshifstutas({
					userId: userInfo.userId
				});
				
				const shifustatus = (shifuStatusRes.data !== undefined && shifuStatusRes.data !== null) ? Number(shifuStatusRes.data) : -1;
				console.log('师傅状态响应:', shifuStatusRes);
				console.log('师傅状态:', shifustatus);

				// 2. 如果未入驻，先进行师傅入驻
				if (shifustatus === -1) {
					// 获取地理位置信息
					const locationData = uni.getStorageSync('locationData') || {};
					
					const masterEnterData = {
						userId: userInfo.userId,
						mobile: userInfo.phone,
						address: locationData.address || '',
						cityId: '1046,1127,1135', // 默认城市ID
						labelId: 25, // 默认标签ID
						lng: locationData.lng || 0,
						lat: locationData.lat || 0,
					};

					console.log('师傅入驻数据:', masterEnterData);
					const enterRes = await this.$api.shifu.masterEnter(masterEnterData);
					
					if (enterRes.code !== "200") {
						throw new Error('师傅入驻失败');
					}
					console.log('师傅入驻成功');
				}

				// 3. 获取师傅详细信息
				const masterRes = await this.$api.shifu.getMaster();
				if (!masterRes || !masterRes.data) {
					throw new Error('获取师傅信息失败');
				}

				const masterInfo = masterRes.data;
				console.log('师傅详细信息:', masterInfo);

				// 4. 更新用户信息，优先使用师傅信息，但如果师傅信息为空则使用用户信息
				const updatedUserInfo = {
					phone: (masterInfo.mobile && masterInfo.mobile.trim()) ? masterInfo.mobile : userInfo.phone,
					avatarUrl: (masterInfo.avatarUrl && masterInfo.avatarUrl.trim()) ? masterInfo.avatarUrl : userInfo.avatarUrl,
					nickName: (masterInfo.coachName && masterInfo.coachName.trim()) ? masterInfo.coachName : userInfo.nickName,
					userId: userInfo.userId, // 保持原用户ID
					shufuId: masterInfo.id || '', // 师傅ID
					createTime: userInfo.createTime,
					pid: masterInfo.pid || userInfo.pid,
					inviteCode: userInfo.inviteCode
				};

				console.log('合并后的用户信息:', updatedUserInfo);

				// 5. 如果师傅信息为空，更新师傅资料
				if (!masterInfo.coachName || !masterInfo.mobile) {
					console.log('师傅信息不完整，需要更新师傅资料');
					try {
						const updateData = {
							coachName: userInfo.nickName,
							mobile: userInfo.phone,
							avatarUrl: userInfo.avatarUrl
						};
						console.log('更新师傅资料数据:', updateData);
						
						// 调用更新师傅信息的接口
						await this.$api.shifu.updataInfoSF(updateData);
						console.log('师傅资料更新成功');
						
						// 重新获取师傅信息
						const updatedMasterRes = await this.$api.shifu.getMaster();
						if (updatedMasterRes && updatedMasterRes.data) {
							Object.assign(masterInfo, updatedMasterRes.data);
							console.log('重新获取师傅信息成功:', masterInfo);
						}
					} catch (updateError) {
						console.error('更新师傅资料失败:', updateError);
						// 即使更新失败，也继续使用用户信息
					}
				}

				// 6. 保存师傅信息到本地存储
				const shiInfo = {
					mobile: updatedUserInfo.phone,
					avatarUrl: updatedUserInfo.avatarUrl,
					coachName: updatedUserInfo.nickName,
					id: masterInfo.id || '',
					userId: userInfo.userId,
					pid: updatedUserInfo.pid,
					status: shifustatus,
					messagePush: Number(masterInfo.messagePush) || -1
				};

				uni.setStorageSync('shiInfo', JSON.stringify(shiInfo));
				console.log('保存师傅信息到本地存储:', shiInfo);

				// 6. 更新Vuex中的用户信息
				this.updateUserItem({
					key: 'userInfo',
					val: updatedUserInfo
				});

				// 7. 更新本地存储的用户信息
				this.saveUserInfoToStorage(updatedUserInfo);

				console.log('师傅信息获取和保存完成');

			} catch (error) {
				console.error('fetchShifuInfoAfterLogin error:', error);
				throw error;
			}
		},

		// 保存用户信息到本地存储
		saveUserInfoToStorage(userInfo) {
			uni.setStorageSync('phone', userInfo.phone);
			uni.setStorageSync('avatarUrl', userInfo.avatarUrl);
			uni.setStorageSync('nickName', userInfo.nickName);
			uni.setStorageSync('userId', userInfo.userId);
			uni.setStorageSync('pid', userInfo.pid);
			if (userInfo.unionid) {
				uni.setStorageSync('unionid', userInfo.unionid);
			}
		},

		// 微信登录
		async handleWechatLogin() {
			if (this.isWechatLoading) return;

			// 检查是否同意协议
			if (!this.agreedToTerms) {
				this.showToast('请勾选我已阅读并同意服务协议和隐私政策');
				return;
			}

			console.log('=== 开始APP微信登录流程 ===');
			this.isWechatLoading = true;
			uni.showLoading({ title: '正在启动微信...' });

			try {
				// 第一步：检查微信登录环境
				console.log('步骤1: 检查登录环境');
				await this.checkWechatEnvironment();

				// 第二步：获取微信授权码
				console.log('步骤2: 获取微信授权码');
				uni.showLoading({ title: '正在获取授权...' });
				const code = await this.getWechatCode();
				console.log('获取到授权码:', code);

				// 第三步：调用后端登录接口
				console.log('步骤3: 调用登录接口');
				uni.showLoading({ title: '登录中...' });
				await this.callWechatLoginAPI(code);

				console.log('=== 微信登录流程完成 ===');

			} catch (error) {
				console.error('微信登录失败:', error);

				// 根据错误类型提供不同的提示
				let errorMessage = error.message || '微信登录失败';
				if (errorMessage.includes('取消')) {
					// 用户主动取消，不显示错误提示
					console.log('用户取消微信登录');
				} else {
					this.showToast(errorMessage);
				}
			} finally {
				this.isWechatLoading = false;
				uni.hideLoading();
			}
		},

		// 检查微信登录环境
		async checkWechatEnvironment() {
			console.log('检查微信登录环境...');

			// 检查当前平台
			const platform = this.getCurrentPlatform();
			console.log('当前平台:', platform);

			// 只在APP环境下支持微信登录
			if (platform !== 'app-plus') {
				if (platform === 'h5') {
					throw new Error('H5环境不支持微信登录，请下载APP使用');
				} else if (platform === 'mp-weixin') {
					throw new Error('请使用小程序原生登录方式');
				} else {
					throw new Error('当前环境不支持微信登录，请在APP中使用');
				}
			}

			// 检查设备是否安装微信
			// #ifdef APP-PLUS
			console.log('开始检查微信安装状态...');
			try {
				const isWechatInstalled = await new Promise((resolve) => {
					console.log('调用plus.runtime.isApplicationExist检查微信...');

					// 添加超时处理
					const timeout = setTimeout(() => {
						console.warn('检查微信安装状态超时，跳过此检查');
						resolve(true); // 超时时假设已安装，继续流程
					}, 5000); // 5秒超时

					plus.runtime.isApplicationExist({
						pname: 'com.tencent.mm', // Android微信包名
						action: 'weixin://' // iOS微信scheme
					}, (result) => {
						clearTimeout(timeout);
						console.log('微信安装检查结果:', result);
						resolve(result);
					});
				});

				console.log('微信安装状态检查完成:', isWechatInstalled);
				if (!isWechatInstalled) {
					throw new Error('设备未安装微信，请先安装微信客户端');
				}
			} catch (error) {
				console.warn('检查微信安装状态失败:', error);
				// 不阻止登录流程，继续尝试
			}
			// #endif

			// 检查OAuth服务提供商
			console.log('开始检查OAuth服务提供商...');
			try {
				const providers = await new Promise((resolve, reject) => {
					console.log('调用uni.getProvider获取OAuth服务...');

					// 添加超时处理
					const timeout = setTimeout(() => {
						console.error('获取OAuth服务超时');
						reject(new Error('获取OAuth服务超时，请检查网络连接'));
					}, 10000); // 10秒超时

					uni.getProvider({
						service: 'oauth',
						success: (res) => {
							clearTimeout(timeout);
							console.log('获取OAuth服务成功:', res);
							resolve(res);
						},
						fail: (err) => {
							clearTimeout(timeout);
							console.error('获取OAuth服务失败:', err);
							reject(err);
						}
					});
				});

				console.log('OAuth提供商:', providers);

				if (!providers.provider || !providers.provider.includes('weixin')) {
					throw new Error('微信登录服务未配置，请联系开发者');
				}

				console.log('OAuth服务检查完成，微信登录服务可用');
			} catch (error) {
				console.error('获取OAuth服务失败:', error);
				throw new Error('获取登录服务失败，请检查网络连接或重启APP');
			}

			console.log('微信登录环境检查完成');
		},

		// 获取微信授权码
		async getWechatCode() {
			console.log('获取微信授权码...');

			return new Promise((resolve, reject) => {
				console.log('开始调用uni.login获取微信授权码...');

				// 设置超时
				const timeout = setTimeout(() => {
					console.error('微信授权超时，30秒内未收到响应');
					reject(new Error('微信授权超时，请重试'));
				}, 30000); // 30秒超时

				console.log('调用uni.login，参数:', {
					provider: "weixin",
					onlyAuthorize: true
				});

				uni.login({
					"provider": "weixin",
					"onlyAuthorize": true, // 微信登录仅请求授权认证
					success: (res) => {
						clearTimeout(timeout);
						console.log('获取微信code成功:', res);


						if (res.code) {
							console.log('微信授权码获取成功，code:', res.code);
							resolve(res.code);
						} else {
							console.error('微信返回结果中没有code字段:', res);
							reject(new Error('微信返回的授权码为空'));
						}
					},
					fail: (err) => {
						clearTimeout(timeout);
						console.error('获取微信code失败:', err);

						// APP环境下的错误处理
						let errorMsg = '微信授权失败';

						// 根据错误码处理
						if (err.code) {
							switch (err.code) {
								case 1000:
									errorMsg = '用户取消了微信授权';
									break;
								case 1001:
									errorMsg = '微信授权被拒绝';
									break;
								case 1002:
									errorMsg = '网络错误，请检查网络连接';
									break;
								case 1003:
									errorMsg = '用户点击了拒绝按钮';
									break;
								case 1004:
									errorMsg = '应用未安装微信';
									break;
								case 1005:
									errorMsg = '微信版本过低，请更新微信';
									break;
								default:
									errorMsg = `微信授权失败 (错误码: ${err.code})`;
							}
						} else if (err.errMsg) {
							errorMsg = err.errMsg;
						} else if (err.message) {
							errorMsg = err.message;
						}

						reject(new Error(errorMsg));
					}
				});
			});
		},

		// 获取微信用户信息
		async getWechatUserInfo() {
			console.log('获取微信用户信息...');

			// 在APP环境下，用户信息获取是可选的
			// 很多情况下只需要code就可以完成登录
			return new Promise((resolve) => {
				// 设置较短的超时时间，因为这不是必需的
				const timeout = setTimeout(() => {
					console.log('获取用户信息超时，使用空数据继续');
					resolve({
						encryptedData: '',
						iv: ''
					});
				}, 10000); // 10秒超时

				uni.getUserInfo({
					provider: 'weixin',
					success: (res) => {
						clearTimeout(timeout);
						console.log('获取用户信息成功:', res);
						resolve({
							encryptedData: res.encryptedData || '',
							iv: res.iv || ''
						});
					},
					fail: (err) => {
						clearTimeout(timeout);
						console.warn('获取用户信息失败，继续登录流程:', err);
						// 在APP环境下，用户信息获取失败是常见的，不影响登录
						resolve({
							encryptedData: '',
							iv: ''
						});
					}
				});
			});
		},

		// 调用后端微信登录接口
		async callWechatLoginAPI(code) {
			console.log('调用后端微信登录接口...');

			// 获取极光推送ID
			// const registrationId = await this.getRegistrationId();
			// console.log('极光推送ID:', registrationId);

			const params = {
				code: code,
				platform: 1, // 1表示师傅端，2表示用户端
				registrationId: this.registerID
			};

			console.log('登录参数:', params);

			try {
				const response = await this.$api.base.appLoginByWechat(params);
				console.log('登录接口响应:', response);

				if (response && response.code === '200') {
					await this.handleWechatLoginSuccess(response);
					this.showToast('微信登录成功', 'success');
				} else {
					throw new Error(response?.msg || '登录失败，请重试');
				}
			} catch (error) {
				console.error('调用登录接口失败:', error);
				throw new Error('网络请求失败，请检查网络连接');
			}
		},

		// 处理微信登录成功 - 新方法专门处理用户数据
		async handleWechatLoginSuccess(response) {
			console.log('=== 开始处理微信登录成功响应 ===');
			console.log('微信登录响应:', response);

			if (!response || response.code !== '200') {
				throw new Error(response?.msg || '微信登录失败');
			}

			// 微信登录返回的数据结构：
			// code: "200"
			// data: { 用户信息对象 }
			// header: {...}

			// 从响应中获取用户数据
			const userData = response.data;
			console.log('微信登录获取到的用户数据:', userData);

			if (!userData || !userData.id) {
				console.error('微信登录响应中未找到用户数据');
				throw new Error('微信登录失败，未获取到用户数据');
			}

			// 从响应头中提取token（如果有的话）
			const token = this.extractTokenFromHeaders(response.header);
			console.log('从响应头提取的token:', token);

			if (token) {
				// 保存token
				uni.setStorageSync('token', token);

				// 确保 Vuex 更新完成
				await this.$nextTick();
				this.updateUserItem({
					key: 'autograph',
					val: token
				});

				// 再次等待确保状态更新完成
				await this.$nextTick();

				console.log('微信登录token已保存:', {
					storage: uni.getStorageSync('token'),
					vuex: this.$store.state.user.autograph
				});
			}

			// 格式化用户信息
			const userInfoFormatted = {
				phone: userData.phone || '',
				avatarUrl: userData.avatarUrl || '/static/mine/default_user.png',
				nickName: (userData.nickName && userData.nickName.trim()) ? userData.nickName : '微信用户',
				userId: userData.id || '',
				createTime: userData.createTime || '',
				pid: userData.pid || '',
				inviteCode: userData.inviteCode || '',
				unionid: userData.unionid || '', // 保存unionid用于后续绑定手机号
				appOpenid: userData.appOpenid || '' // 保存appOpenid
			};

			// 保存用户信息到Vuex
			console.log('保存用户信息到Vuex:', userInfoFormatted);
			await this.$nextTick();
			this.updateUserItem({
				key: 'userInfo',
				val: userInfoFormatted
			});

			// 等待 Vuex 更新完成
			await this.$nextTick();

			// 保存到本地存储
			console.log('保存用户信息到本地存储');
			this.saveUserInfoToStorage(userInfoFormatted);

			// 额外保存unionid到本地存储，用于绑定手机号
			if (userData.unionid) {
				console.log('保存unionid到本地存储:', userData.unionid);
				uni.setStorageSync('unionid', userData.unionid);
			}

			// 验证保存结果
			console.log('验证保存结果:');
			console.log('本地存储userId:', uni.getStorageSync('userId'));
			console.log('本地存储nickName:', uni.getStorageSync('nickName'));
			console.log('本地存储unionid:', uni.getStorageSync('unionid'));
			console.log('Vuex中的token:', this.$store.state.user.autograph);

			console.log('微信用户信息已保存:', userInfoFormatted);
			console.log('=== 微信登录成功处理完成 ===');

			// 确保所有数据保存完成后再跳转
			await this.$nextTick();

			// 跳转到个人页面
			setTimeout(() => {
				uni.reLaunch({
					url: '/pages/mine'
				});
			}, 800); // 减少延迟时间
		},

		// 获取极光推送ID
		async getRegistrationId() {
			try {
				// 尝试从存储中获取
				const storedId = uni.getStorageSync('registrationId');
				if (storedId) {
					console.log('从存储中获取到极光推送ID:', storedId);
					return storedId;
				}

				// 如果存储中没有，尝试从极光推送插件获取
				// #ifdef APP-PLUS
				if (typeof plus !== 'undefined' && plus.push) {
					return new Promise((resolve) => {
						plus.push.getClientInfo((info) => {
							console.log('从极光推送获取到ID:', info.clientid);
							if (info.clientid) {
								uni.setStorageSync('registrationId', info.clientid);
							}
							resolve(info.clientid || '');
						}, (error) => {
							console.warn('获取极光推送ID失败:', error);
							resolve('');
						});
					});
				}
				// #endif

				console.log('无法获取极光推送ID，返回空字符串');
				return '';
			} catch (error) {
				console.warn('获取极光推送ID异常:', error);
				return '';
			}
		},

		// 获取邀请码
		getInviteCode() {
			// 可以从页面参数、存储等地方获取邀请码
			return this.registerForm.pidInviteCode || '';
		},

		// 获取密码强度
		getPasswordStrength() {
			const password = this.registerForm.password;
			if (!password) return 0;

			let strength = 0;

			// 长度检查
			if (password.length >= 6) strength++;

			// 包含数字和字母
			if (/[0-9]/.test(password) && /[a-zA-Z]/.test(password)) strength++;

			// 包含特殊字符或长度超过8位
			if (/[^a-zA-Z0-9]/.test(password) || password.length >= 8) strength++;

			return strength;
		},

		// 获取密码强度文本
		getPasswordStrengthText() {
			const strength = this.getPasswordStrength();
			switch (strength) {
				case 1: return '弱';
				case 2: return '中';
				case 3: return '强';
				default: return '';
			}
		},

		// 显示提示信息
		showToast(title, icon = 'none') {
			uni.showToast({
				title,
				icon,
				duration: 2000
			});
		}
	}
};
</script>

<style lang="scss" scoped>
.login-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
	position: relative;
	overflow: hidden;
}

.bg-decoration {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	pointer-events: none;

	.circle {
		position: absolute;
		border-radius: 50%;
		background: rgba(59, 130, 246, 0.08);
		animation: float 8s ease-in-out infinite;

		&.circle-1 {
			width: 200rpx;
			height: 200rpx;
			top: 10%;
			right: -50rpx;
			animation-delay: 0s;
		}

		&.circle-2 {
			width: 150rpx;
			height: 150rpx;
			top: 60%;
			left: -30rpx;
			animation-delay: 3s;
		}

		&.circle-3 {
			width: 100rpx;
			height: 100rpx;
			top: 30%;
			left: 50%;
			animation-delay: 6s;
		}
	}
}

@keyframes float {

	0%,
	100% {
		transform: translateY(0px) rotate(0deg);
		opacity: 0.6;
	}

	50% {
		transform: translateY(-15px) rotate(180deg);
		opacity: 0.3;
	}
}

.header {
	padding: 80rpx 40rpx 40rpx;
	position: relative;
	z-index: 10;

	.back-btn {
		width: 60rpx;
		height: 60rpx;
		background: rgba(59, 130, 246, 0.1);
		backdrop-filter: blur(10rpx);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		border: 1rpx solid rgba(59, 130, 246, 0.2);

		&:active {
			background: rgba(59, 130, 246, 0.15);
			transform: scale(0.95);
		}
	}
}

.logo-section {
	text-align: center;
	padding: 0 40rpx;
	margin-bottom: 60rpx;
	position: relative;
	z-index: 10;

	.logo-wrapper {
		position: relative;
		display: inline-block;
		margin-bottom: 32rpx;

		&::before {
			content: '';
			position: absolute;
			top: -10rpx;
			left: -10rpx;
			right: -10rpx;
			bottom: -10rpx;
			background: linear-gradient(45deg, rgba(59, 130, 246, 0.15), rgba(59, 130, 246, 0.05));
			border-radius: 50%;
			animation: pulse 3s ease-in-out infinite;
		}

		.logo {
			width: 100rpx;
			height: 100rpx;
			border-radius: 50%;
			position: relative;
			z-index: 1;
			box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.15);
		}
	}

	.app-name {
		font-size: 52rpx;
		font-weight: 700;
		color: #1e293b;
		margin-bottom: 16rpx;
		text-shadow: none;
	}

	.welcome-text {
		font-size: 30rpx;
		color: #64748b;
		line-height: 1.5;
		font-weight: 400;
	}
}

@keyframes pulse {

	0%,
	100% {
		transform: scale(1);
		opacity: 0.8;
	}

	50% {
		transform: scale(1.05);
		opacity: 0.4;
	}
}

.main-card {
	background: rgba(255, 255, 255, 0.95);
	backdrop-filter: blur(20rpx);
	border-radius: 32rpx 32rpx 0 0;
	margin: 0 20rpx;
	padding: 60rpx 40rpx 40rpx;
	box-shadow: 0 -8rpx 32rpx rgba(59, 130, 246, 0.08);
	position: relative;
	z-index: 10;
	min-height: calc(100vh - 400rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.8);

	&::before {
		content: '';
		position: absolute;
		top: 20rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 60rpx;
		height: 6rpx;
		background: #cbd5e1;
		border-radius: 3rpx;
	}

	.mode-title {
		text-align: center;
		font-size: 48rpx;
		font-weight: 700;
		color: #1e293b;
		margin-bottom: 60rpx;
		position: relative;

		&::after {
			content: '';
			position: absolute;
			bottom: -20rpx;
			left: 50%;
			transform: translateX(-50%);
			width: 80rpx;
			height: 4rpx;
			background: linear-gradient(90deg, #3b82f6, #1d4ed8);
			border-radius: 2rpx;
		}
	}

	.form-content {
		margin-top: 40rpx;
	}
}

.tab-switcher {
	display: flex;
	background: #f1f5f9;
	border-radius: 16rpx;
	margin-bottom: 50rpx;
	padding: 6rpx;
	position: relative;
	box-shadow: inset 0 1rpx 3rpx rgba(0, 0, 0, 0.05);

	.tab-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 16rpx;
		border-radius: 12rpx;
		font-size: 28rpx;
		color: #64748b;
		transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
		position: relative;
		cursor: pointer;

		text {
			margin-left: 8rpx;
		}

		&.active {
			background: linear-gradient(135deg, #3b82f6, #1d4ed8);
			color: #fff;
			font-weight: 600;
			box-shadow: 0 4rpx 12rpx rgba(59, 130, 246, 0.25);
			transform: translateY(-1rpx);

			text {
				color: #fff;
			}
		}

		&:not(.active):active {
			transform: scale(0.98);
		}
	}
}

.input-group {
	.input-item {
		display: flex;
		align-items: center;
		background: #f8fafc;
		border: 2rpx solid #e2e8f0;
		border-radius: 20rpx;
		margin-bottom: 32rpx;
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;

		&:focus-within {
			border-color: #3b82f6;
			background: #fff;
			box-shadow: 0 0 0 6rpx rgba(59, 130, 246, 0.1);
		}

		&.optional {
			.input-label::after {
				content: ' (可获得奖励)';
				color: #f59e0b;
				font-size: 22rpx;
			}
		}

		.input-icon {
			padding: 32rpx 24rpx 32rpx 32rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.input-content {
			flex: 1;
			padding: 16rpx 0;

			.input-label {
				font-size: 24rpx;
				color: #64748b;
				margin-bottom: 8rpx;
				font-weight: 500;

				.optional-tag {
					color: #f59e0b;
					font-size: 20rpx;
					margin-left: 8rpx;
					background: rgba(245, 158, 11, 0.1);
					padding: 2rpx 8rpx;
					border-radius: 6rpx;
				}
			}

			.input-field {
				width: 100%;
				font-size: 32rpx;
				color: #1e293b;
				background: transparent;
				border: none;
				outline: none;

				&::placeholder {
					color: #94a3b8;
					font-size: 30rpx;
				}
			}
		}

		.input-field {
			flex: 1;
			padding: 32rpx 0;
			font-size: 32rpx;
			color: #1e293b;
			background: transparent;
			border: none;
			outline: none;

			&::placeholder {
				color: #94a3b8;
				font-size: 30rpx;
			}
		}

		.action-icon {
			padding: 32rpx 32rpx 32rpx 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			cursor: pointer;
			transition: all 0.2s ease;

			&:active {
				transform: scale(0.95);
			}
		}

		.input-status {
			padding: 32rpx 32rpx 32rpx 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.input-benefit {
			padding: 32rpx 32rpx 32rpx 16rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.sms-btn {
			background: linear-gradient(135deg, #3b82f6, #1d4ed8);
			color: #fff;
			padding: 20rpx 24rpx;
			border-radius: 12rpx;
			font-size: 26rpx;
			font-weight: 600;
			margin-right: 16rpx;
			transition: all 0.3s ease;
			border: none;
			cursor: pointer;
			white-space: nowrap;

			&:active {
				transform: scale(0.98);
			}

			&.disabled {
				background: #94a3b8;
				cursor: not-allowed;
				transform: none;

				&:active {
					transform: none;
				}
			}
		}
	}
}

.password-strength {
	display: flex;
	align-items: center;
	margin: -16rpx 0 32rpx 0;
	padding: 0 32rpx;

	.strength-label {
		font-size: 24rpx;
		color: #64748b;
		margin-right: 16rpx;
	}

	.strength-bar {
		display: flex;
		gap: 8rpx;
		margin-right: 16rpx;

		.strength-item {
			width: 40rpx;
			height: 6rpx;
			background: #e2e8f0;
			border-radius: 3rpx;
			transition: all 0.3s ease;

			&.active {
				&:nth-child(1) {
					background: #ef4444; // 弱 - 红色
				}

				&:nth-child(2) {
					background: #f59e0b; // 中 - 橙色
				}

				&:nth-child(3) {
					background: #10b981; // 强 - 绿色
				}
			}
		}
	}

	.strength-text {
		font-size: 24rpx;
		font-weight: 600;

		&:nth-of-type(1) {
			color: #ef4444; // 弱
		}

		&:nth-of-type(2) {
			color: #f59e0b; // 中
		}

		&:nth-of-type(3) {
			color: #10b981; // 强
		}
	}
}

.login-links {
	display: flex;
	justify-content: space-between;
	align-items: center;
	margin-top: 32rpx;

	.forgot-link {
		color: #3b82f6;
		font-size: 28rpx;
		font-weight: 500;
		cursor: pointer;
		transition: color 0.2s ease;

		&:active {
			color: #1d4ed8;
		}
	}

	.register-link {
		color: #64748b;
		font-size: 28rpx;

		.link-highlight {
			color: #3b82f6;
			font-weight: 600;
			cursor: pointer;
			transition: color 0.2s ease;

			&:active {
				color: #1d4ed8;
			}
		}
	}
}

.agreement-section {
	margin: 40rpx 0;

	.checkbox-container {
		display: flex;
		align-items: flex-start;
		cursor: pointer;

		.checkbox {
			width: 36rpx;
			height: 36rpx;
			border: 2rpx solid #cbd5e1;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-right: 16rpx;
			margin-top: 2rpx;
			flex-shrink: 0;
			transition: all 0.3s ease;
			background: #fff;

			&.checked {
				background: linear-gradient(135deg, #3b82f6, #1d4ed8);
				border-color: #3b82f6;
				transform: scale(1.05);
			}

			&:active {
				transform: scale(0.95);
			}
		}

		.agreement-text {
			font-size: 26rpx;
			color: #64748b;
			line-height: 1.6;
			flex: 1;

			.link {
				color: #3b82f6;
				font-weight: 500;
				cursor: pointer;
				transition: color 0.2s ease;

				&:active {
					color: #1d4ed8;
				}
			}
		}
	}
}

.action-button {
	width: 100%;
	height: 100rpx;
	background: linear-gradient(135deg, #3b82f6, #1d4ed8);
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 36rpx;
	font-weight: 700;
	margin: 40rpx 0 32rpx;
	box-shadow: 0 8rpx 25rpx rgba(59, 130, 246, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border: none;
	cursor: pointer;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s ease;
	}

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 15rpx rgba(59, 130, 246, 0.4);

		&::before {
			left: 100%;
		}
	}

	&.disabled {
		background: #94a3b8;
		box-shadow: none;
		cursor: not-allowed;
		transform: none;

		&:active {
			transform: none;
		}

		&::before {
			display: none;
		}
	}

	.loading-icon {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.3);
		border-top: 3rpx solid #fff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-right: 16rpx;
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.wechat-login-button {
	width: 100%;
	height: 100rpx;
	background: #07c160;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #fff;
	font-size: 36rpx;
	font-weight: 700;
	margin-bottom: 32rpx;
	box-shadow: 0 8rpx 25rpx rgba(7, 193, 96, 0.3);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border: none;
	cursor: pointer;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s ease;
	}

	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 4rpx 15rpx rgba(7, 193, 96, 0.4);

		&::before {
			left: 100%;
		}
	}

	&.disabled {
		background: #94a3b8;
		box-shadow: none;
		cursor: not-allowed;
		transform: none;

		&:active {
			transform: none;
		}

		&::before {
			display: none;
		}
	}

	.loading-icon {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid rgba(255, 255, 255, 0.3);
		border-top: 3rpx solid #fff;
		border-radius: 50%;
		animation: spin 1s linear infinite;
		margin-right: 16rpx;
	}

	.wechat-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 16rpx;
		background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI4LjMzMzMgMTMuMzMzM0MyOC4zMzMzIDEwLjM4MSAyNS45NTI0IDggMjMgOEgxN0MxNC4wNDc2IDggMTEuNjY2NyAxMC4zODEgMTEuNjY2NyAxMy4zMzMzVjE4LjY2NjdDMTEuNjY2NyAyMS42MTkgMTQuMDQ3NiAyNCAxNyAyNEgyM0MyNS45NTI0IDI0IDI4LjMzMzMgMjEuNjE5IDI4LjMzMzMgMTguNjY2N1YxMy4zMzMzWiIgZmlsbD0id2hpdGUiLz4KPC9zdmc+Cg==') no-repeat center;
		background-size: contain;
	}
}

.switch-links {
	text-align: center;
	margin-top: 20rpx;

	.link-text {
		color: #64748b;
		font-size: 28rpx;
		line-height: 1.5;

		.link-highlight {
			color: #3b82f6;
			font-weight: 600;
			cursor: pointer;
			transition: color 0.2s ease;

			&:active {
				color: #1d4ed8;
			}
		}
	}
}

.bottom-decoration {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 100rpx;
	pointer-events: none;

	.wave {
		position: absolute;
		bottom: 0;
		left: 0;
		width: 100%;
		height: 100rpx;
		background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
		clip-path: polygon(0 50%, 100% 80%, 100% 100%, 0% 100%);
	}
}

// 响应式适配
@media screen and (max-width: 750rpx) {
	.main-card {
		margin: 0 10rpx;
		padding: 50rpx 30rpx 30rpx;
	}

	.logo-section {
		padding: 0 30rpx;
		margin-bottom: 50rpx;

		.app-name {
			font-size: 48rpx;
		}

		.welcome-text {
			font-size: 28rpx;
		}
	}

	.input-group .input-item {
		margin-bottom: 28rpx;

		.input-field {
			font-size: 30rpx;
		}

		.sms-btn {
			font-size: 24rpx;
			padding: 18rpx 20rpx;
		}
	}

	.action-button,
	.wechat-login-button {
		height: 96rpx;
		font-size: 34rpx;
	}
}
</style>
