@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.location-guard.data-v-fa26c7d8 {
  position: relative;
  width: 100%;
  height: 100%;
}
.loading-overlay.data-v-fa26c7d8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}
.loading-overlay .loading-content.data-v-fa26c7d8 {
  text-align: center;
}
.loading-overlay .loading-content .loading-text.data-v-fa26c7d8 {
  font-size: 28rpx;
  color: #666;
}
.permission-denied.data-v-fa26c7d8 {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9998;
}
.permission-denied .denied-content.data-v-fa26c7d8 {
  text-align: center;
  padding: 60rpx 40rpx;
  max-width: 600rpx;
}
.permission-denied .denied-content .denied-icon.data-v-fa26c7d8 {
  font-size: 120rpx;
  margin-bottom: 40rpx;
}
.permission-denied .denied-content .denied-title.data-v-fa26c7d8 {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.permission-denied .denied-content .denied-message.data-v-fa26c7d8 {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 60rpx;
}
.permission-denied .denied-content .denied-buttons.data-v-fa26c7d8 {
  display: flex;
  gap: 30rpx;
  justify-content: center;
}
.permission-denied .denied-content .denied-buttons button.data-v-fa26c7d8 {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
  max-width: 200rpx;
}
.permission-denied .denied-content .denied-buttons button.btn-primary.data-v-fa26c7d8 {
  background-color: #1890ff;
  color: #fff;
}
.permission-denied .denied-content .denied-buttons button.btn-secondary.data-v-fa26c7d8 {
  background-color: #f5f5f5;
  color: #666;
}

