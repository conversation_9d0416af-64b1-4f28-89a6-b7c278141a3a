@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.container.data-v-bdc3f1fe {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding: 20rpx;
}
.header.data-v-bdc3f1fe {
  text-align: center;
  padding: 40rpx 0;
}
.header .title.data-v-bdc3f1fe {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}
.content .status-card.data-v-bdc3f1fe, .content .location-card.data-v-bdc3f1fe, .content .log-card.data-v-bdc3f1fe {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}
.content .status-title.data-v-bdc3f1fe, .content .location-title.data-v-bdc3f1fe, .content .log-title.data-v-bdc3f1fe {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.content .status-content .status-text.data-v-bdc3f1fe, .content .status-content .location-text.data-v-bdc3f1fe, .content .location-content .status-text.data-v-bdc3f1fe, .content .location-content .location-text.data-v-bdc3f1fe {
  display: block;
  font-size: 28rpx;
  margin-bottom: 10rpx;
}
.content .status-content .status-text.success.data-v-bdc3f1fe, .content .status-content .location-text.success.data-v-bdc3f1fe, .content .location-content .status-text.success.data-v-bdc3f1fe, .content .location-content .location-text.success.data-v-bdc3f1fe {
  color: #52c41a;
}
.content .status-content .status-text.error.data-v-bdc3f1fe, .content .status-content .location-text.error.data-v-bdc3f1fe, .content .location-content .status-text.error.data-v-bdc3f1fe, .content .location-content .location-text.error.data-v-bdc3f1fe {
  color: #ff4d4f;
}
.content .status-content .platform-text.data-v-bdc3f1fe, .content .location-content .platform-text.data-v-bdc3f1fe {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-top: 10rpx;
}
.content .button-group.data-v-bdc3f1fe {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
}
.content .btn.data-v-bdc3f1fe {
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}
.content .btn.primary.data-v-bdc3f1fe {
  background-color: #1890ff;
  color: #fff;
}
.content .btn.secondary.data-v-bdc3f1fe {
  background-color: #6c757d;
  color: #fff;
}
.content .btn.success.data-v-bdc3f1fe {
  background-color: #52c41a;
  color: #fff;
}
.content .btn.warning.data-v-bdc3f1fe {
  background-color: #faad14;
  color: #fff;
}
.content .btn.info.data-v-bdc3f1fe {
  background-color: #13c2c2;
  color: #fff;
}
.content .btn.small.data-v-bdc3f1fe {
  height: 60rpx;
  font-size: 24rpx;
  margin-top: 20rpx;
  background-color: #f0f0f0;
  color: #666;
}
.content .log-content.data-v-bdc3f1fe {
  height: 400rpx;
  border: 2rpx solid #f0f0f0;
  border-radius: 8rpx;
  padding: 20rpx;
}
.content .log-content .log-item.data-v-bdc3f1fe {
  display: block;
  font-size: 24rpx;
  color: #666;
  margin-bottom: 10rpx;
  line-height: 1.4;
}

