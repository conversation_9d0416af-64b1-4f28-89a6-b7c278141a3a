<template>
	<view class="page-container">
		<!-- 顶部导航 -->
	
		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-text">加载中...</view>
		</view>

		<!-- 信用信息内容 -->
		<view v-else class="credit-content">
			<!-- 信用评分卡片 -->
			<view class="credit-card">
				<view class="credit-header">
					<view class="credit-title">当前信用分</view>
					<view class="credit-score">{{ currentCredit }}</view>
				</view>
				<view class="credit-level">{{ getCreditLevel(currentCredit) }}</view>
			</view>

			<!-- 筛选条件 -->
			<view class="filter-section">
				<view class="filter-title">筛选条件</view>
				<view class="filter-options">
					<view class="filter-item">
						<view class="filter-label">信誉分类型:</view>
						<view class="filter-dropdown">
							<view class="dropdown-item"
								v-for="(option, index) in typeOptions"
								:key="index"
								:class="{ active: typeIndex === index }"
								@click="selectType(index)">
								{{ option.label }}
							</view>
						</view>
					</view>
					<view class="filter-item">
						<view class="filter-label">变动原因:</view>
						<view class="filter-dropdown">
							<view class="dropdown-item"
								v-for="(option, index) in reasonOptions"
								:key="index"
								:class="{ active: reasonIndex === index }"
								@click="selectReason(index)">
								{{ option.label }}
							</view>
						</view>
					</view>
				</view>
				<view class="filter-actions">
					<button @click="resetFilters" class="reset-btn">重置</button>
					<button @click="handleSearch" class="search-btn">查询</button>
				</view>
			</view>

			<!-- 信用变动记录 -->
			<view class="credit-records">
				<view class="records-title">信用变动记录</view>
				<view v-if="creditRecords.length === 0" class="no-data">
					<view class="no-data-text">暂无记录</view>
				</view>
				<view v-else class="records-list">
					<view class="record-item" v-for="(record, index) in creditRecords" :key="record.id">
						<view class="record-header">
							<view class="record-type" :class="record.creditType === 1 ? 'add' : 'minus'">
								{{ record.creditType === 1 ? '+' : '-' }}{{ record.points }}分
							</view>
							<view class="record-date">{{ formatDate(record.createTime) }}</view>
						</view>
						<view class="record-content">
							<view class="record-reason">{{ getReasonText(record.changeReason) }}</view>
							<view class="record-detail">
								<text>变动前: {{ record.beforePoints }}分</text>
								<text>变动后: {{ record.afterPoints }}分</text>
							</view>
							<view v-if="record.remark" class="record-remark">备注: {{ record.remark }}</view>
						</view>
					</view>
				</view>

				<!-- 分页信息 -->
				<view v-if="totalCount > 0" class="pagination-info">
					<text>共{{ totalCount }}条记录，第{{ pageNum }}/{{ totalPage }}页</text>
				</view>

				<!-- 加载更多 -->
				<view v-if="hasMore" class="load-more" @click="loadMore">
					<view class="load-more-text">{{ loadingMore ? '加载中...' : '加载更多' }}</view>
				</view>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	data() {
		return {
			// 加载状态
			loading: false,
			loadingMore: false,

			// 当前信用分
			currentCredit: 0,

			// 筛选条件
			typeIndex: 0,
			reasonIndex: 0,
			typeOptions: [
				{ label: '全部', value: null },
				{ label: '加分', value: 1 },
				{ label: '扣分', value: 2 }
			],
			reasonOptions: [
				{ label: '全部', value: null },
				{ label: '完成订单', value: 1 },
				{ label: '客户好评', value: 2 },
				{ label: '客户差评', value: 3 },
				// { label: '投诉', value: 4 },
				{ label: '违规', value: 5 },
				{ label: '取消订单', value: 6 }
			],

			// 信用记录数据
			creditRecords: [],
			pageNum: 1,
			pageSize: 10,
			totalCount: 0,
			totalPage: 0,
			hasMore: false
		}
	},
	onLoad() {
		// 页面加载时获取当前信用分
		this.loadCurrentCredit();
		// 获取信用信息 - 首次加载
		console.log('页面加载，首次获取信用信息');
		this.loadCreditInfo(false);
	},
	methods: {
		// 加载当前信用分
		loadCurrentCredit() {
			try {
				// 从本地存储获取师傅信息
				const shiInfo = uni.getStorageSync('shiInfo');
				if (shiInfo) {
					const parsedShiInfo = JSON.parse(shiInfo);
					this.currentCredit = parsedShiInfo.credit || 0;
					console.log('从本地存储获取当前信用分:', this.currentCredit);
				}
			} catch (error) {
				console.error('获取当前信用分失败:', error);
				this.currentCredit = 0;
			}
		},

		// 选择信誉分类型
		selectType(index) {
			this.typeIndex = index;
			// 选择后自动重新查询
			this.loadCreditInfo(false);
		},

		// 选择变动原因
		selectReason(index) {
			this.reasonIndex = index;
			// 选择后自动重新查询
			this.loadCreditInfo(false);
		},

		// 加载信用信息
		async loadCreditInfo(isLoadMore = false) {
			console.log('loadCreditInfo调用:', { isLoadMore, currentRecords: this.creditRecords.length });

			if (!isLoadMore) {
				this.loading = true;
				this.pageNum = 1;
				// 重新查询时必须清空之前的数据
				this.creditRecords = [];
				this.totalCount = 0;
				this.totalPage = 0;
				this.hasMore = false;
				console.log('清空数据，重新查询');
			} else {
				this.loadingMore = true;
				console.log('加载更多，当前页码:', this.pageNum);
			}

			try {
					// 构建请求参数
					const params = {
						pageNum: this.pageNum,
						pageSize: this.pageSize
					};

					// 添加筛选条件
					if (this.typeOptions[this.typeIndex].value !== null) {
						params.type = this.typeOptions[this.typeIndex].value;
					}
					if (this.reasonOptions[this.reasonIndex].value !== null) {
						params.reason = this.reasonOptions[this.reasonIndex].value;
					}

					console.log('请求参数:', params);

					// 调用API
					const response = await this.$api.shifu.getcreditInfo(params);
					console.log('API响应:', response);

					if (response && response.code === '200' && response.data) {
						const data = response.data;

						// 更新当前信用分（从第一条记录获取，或者从师傅信息获取）
						if (data.list && data.list.length > 0 && !isLoadMore) {
							// 只在首次加载时更新当前信用分
							this.currentCredit = data.list[0].afterPoints || this.currentCredit;
						}

						// 处理记录列表 - 确保不会累加错误数据
						const newRecords = data.list || [];

						if (isLoadMore) {
							// 加载更多时，追加新数据
							console.log('追加新数据:', newRecords.length, '条');
							this.creditRecords = [...this.creditRecords, ...newRecords];
						} else {
							// 首次加载或重新查询时，完全替换数据
							console.log('替换所有数据:', newRecords.length, '条');
							this.creditRecords = [...newRecords]; // 使用展开运算符确保是新数组
						}

						// 更新分页信息
						this.totalCount = data.totalCount || 0;
						this.totalPage = data.totalPage || 0;
						this.hasMore = this.pageNum < this.totalPage;

						console.log('信用记录加载成功:', {
							isLoadMore,
							pageNum: this.pageNum,
							newRecords: newRecords.length,
							totalRecords: this.creditRecords.length,
							totalCount: this.totalCount,
							totalPage: this.totalPage,
							hasMore: this.hasMore
						});
					} else {
						throw new Error(response.msg || '获取信用信息失败');
					}
				} catch (error) {
					console.error('获取信用信息失败:', error);
					uni.showToast({
						title: error.message || '获取信用信息失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
					this.loadingMore = false;
				}
			},

			// 加载更多
			loadMore() {
				if (this.hasMore && !this.loadingMore) {
					this.pageNum++;
					this.loadCreditInfo(true);
				}
			},

			// 查询按钮点击
			handleSearch() {
				console.log('手动查询，清空数据重新加载');
				this.loadCreditInfo(false);
			},

			// 重置筛选条件
			resetFilters() {
				this.typeIndex = 0;
				this.reasonIndex = 0;
				console.log('重置筛选条件，清空数据重新加载');
				// 重置后重新查询，确保清空之前的数据
				this.loadCreditInfo(false);
			},

			// 获取信用等级
			getCreditLevel(score) {
				if (score >= 90) return '优秀师傅';
				if (score >= 80) return '良好师傅';
				if (score >= 70) return '合格师傅';
				if (score >= 60) return '待改进师傅';
				return '需要提升';
			},

			// 获取变动原因文本
			getReasonText(reason) {
				const reasonMap = {
					1: '完成订单',
					2: '客户好评',
					3: '客户差评',
					4: '投诉',
					5: '违规',
					6: '取消订单'
				};
				return reasonMap[reason] || '未知原因';
			},

			// 格式化日期
			formatDate(dateStr) {
				if (!dateStr) return '';
				const date = new Date(dateStr);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				return `${year}-${month}-${day} ${hours}:${minutes}`;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		min-height: 100vh;
		background-color: #f5f5f5;
	}

	.header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 20rpx 30rpx;
		color: white;
		position: sticky;
		top: 0;
		z-index: 100;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		text-align: center;
	}

	.loading-container {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400rpx;
	}

	.loading-text {
		font-size: 28rpx;
		color: #999;
	}

	.credit-content {
		padding: 30rpx;
	}

	.credit-card {
		background: linear-gradient(135deg, #667eea 0%, #599EFF 100%);
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		color: white;
		box-shadow: 0 8rpx 30rpx rgba(102, 126, 234, 0.3);
	}

	.credit-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 20rpx;
	}

	.credit-title {
		font-size: 32rpx;
		font-weight: bold;
	}

	.credit-score {
		font-size: 48rpx;
		font-weight: bold;
	}

	.credit-level {
		font-size: 28rpx;
		opacity: 0.9;
	}

	.filter-section {
		background: white;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.filter-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333;
	}

	.filter-options {
		margin-bottom: 30rpx;
	}

	.filter-item {
		margin-bottom: 30rpx;
	}

	.filter-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 15rpx;
		font-weight: bold;
	}

	.filter-dropdown {
		display: flex;
		flex-wrap: wrap;
		gap: 15rpx;
	}

	.dropdown-item {
		font-size: 26rpx;
		color: #666;
		padding: 15rpx 25rpx;
		background: #f8f9fa;
		border-radius: 25rpx;
		border: 2rpx solid #e9ecef;
		cursor: pointer;
		transition: all 0.3s ease;

		&.active {
			background: linear-gradient(135deg, #599EFF 0%, #599EFF 100%);
			color: white;
			border-color: #599EFF;
			transform: scale(1.05);
		}

		&:hover {
			background: #e9ecef;
		}

		&.active:hover {
			background: linear-gradient(135deg, #599EFF 0%, #599EFF 100%);
		}
	}

	.filter-actions {
		display: flex;
		gap: 20rpx;
	}

	.reset-btn, .search-btn {
		flex: 1;
		height: 80rpx;
		border-radius: 10rpx;
		font-size: 28rpx;
		border: none;
	}

	.reset-btn {
		background: #f8f9fa;
		color: #666;
	}

	.search-btn {
		background: linear-gradient(135deg, #599EFF 0%, #599EFF 100%);
		color: white;
	}

	.credit-records {
		background: white;
		border-radius: 20rpx;
		padding: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	}

	.records-title {
		font-size: 32rpx;
		font-weight: bold;
		margin-bottom: 20rpx;
		color: #333;
	}

	.no-data {
		text-align: center;
		padding: 80rpx 0;
	}

	.no-data-text {
		font-size: 28rpx;
		color: #999;
	}

	.records-list {
		.record-item {
			border-bottom: 1px solid #f0f0f0;
			padding: 20rpx 0;

			&:last-child {
				border-bottom: none;
			}
		}
	}

	.record-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 10rpx;
	}

	.record-type {
		font-size: 32rpx;
		font-weight: bold;

		&.add {
			color: #52c41a;
		}

		&.minus {
			color: #ff4d4f;
		}
	}

	.record-date {
		font-size: 24rpx;
		color: #999;
	}

	.record-content {
		padding-left: 20rpx;
	}

	.record-reason {
		font-size: 28rpx;
		color: #333;
		margin-bottom: 10rpx;
	}

	.record-detail {
		font-size: 24rpx;
		color: #666;
		margin-bottom: 10rpx;

		text {
			margin-right: 20rpx;
		}
	}

	.record-remark {
		font-size: 24rpx;
		color: #999;
		font-style: italic;
	}

	.pagination-info {
		text-align: center;
		padding: 20rpx 0;
		font-size: 24rpx;
		color: #999;
	}

	.load-more {
		text-align: center;
		padding: 30rpx 0;
		cursor: pointer;
	}

	.load-more-text {
		font-size: 28rpx;
		color: #667eea;
	}
</style>
