<view class="page data-v-1887afd2"><location-permission-guard vue-id="0f226918-1" auto-check="{{true}}" required="{{true}}" message="师傅端需要获取您的位置信息来推荐附近的订单，请开启定位权限。" data-event-opts="{{[['^permissionGranted',[['onPermissionGranted']]],['^permissionDenied',[['onPermissionDenied']]],['^permissionCancelled',[['onPermissionCancelled']]],['^openSettings',[['onOpenSettings']]]]}}" bind:permissionGranted="__e" bind:permissionDenied="__e" bind:permissionCancelled="__e" bind:openSettings="__e" class="data-v-1887afd2" bind:__l="__l" vue-slots="{{['default']}}"><view class="content data-v-1887afd2"><view class="header data-v-1887afd2"><text class="title data-v-1887afd2">师傅接单大厅</text><text class="subtitle data-v-1887afd2">定位权限已开启</text></view><view class="location-card data-v-1887afd2"><view class="card-title data-v-1887afd2">当前接单位置</view><view class="location-info data-v-1887afd2"><text class="location-text data-v-1887afd2">{{currentLocation||'获取位置中...'}}</text><button data-event-opts="{{[['tap',[['refreshLocation',['$event']]]]]}}" class="refresh-btn data-v-1887afd2" bindtap="__e">刷新位置</button></view></view><view class="order-list data-v-1887afd2"><view class="card-title data-v-1887afd2">附近订单</view><block wx:for="{{orderList}}" wx:for-item="order" wx:for-index="index" wx:key="index"><view class="order-item data-v-1887afd2"><view class="order-info data-v-1887afd2"><text class="order-title data-v-1887afd2">{{order.title}}</text><text class="order-distance data-v-1887afd2">{{"距离："+order.distance}}</text></view><view class="order-price data-v-1887afd2">{{"¥"+order.price}}</view></view></block><block wx:if="{{$root.g0===0}}"><view class="empty-tip data-v-1887afd2"><text class="data-v-1887afd2">暂无附近订单</text></view></block></view><view class="action-buttons data-v-1887afd2"><button data-event-opts="{{[['tap',[['startReceiving',['$event']]]]]}}" class="action-btn primary data-v-1887afd2" bindtap="__e">开始接单</button><button data-event-opts="{{[['tap',[['viewProfile',['$event']]]]]}}" class="action-btn secondary data-v-1887afd2" bindtap="__e">个人中心</button></view></view></location-permission-guard></view>