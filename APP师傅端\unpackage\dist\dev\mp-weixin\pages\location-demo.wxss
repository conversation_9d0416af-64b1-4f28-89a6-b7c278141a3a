@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.page.data-v-1887afd2 {
  min-height: 100vh;
  background-color: #f5f5f5;
}
.content.data-v-1887afd2 {
  padding: 20rpx;
}
.header.data-v-1887afd2 {
  text-align: center;
  padding: 40rpx 0;
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 30rpx;
}
.header .title.data-v-1887afd2 {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}
.header .subtitle.data-v-1887afd2 {
  font-size: 24rpx;
  color: #52c41a;
}
.location-card.data-v-1887afd2, .order-list.data-v-1887afd2 {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
}
.location-card .card-title.data-v-1887afd2, .order-list .card-title.data-v-1887afd2 {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}
.location-info.data-v-1887afd2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.location-info .location-text.data-v-1887afd2 {
  font-size: 28rpx;
  color: #666;
  flex: 1;
}
.location-info .refresh-btn.data-v-1887afd2 {
  background-color: #1890ff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}
.order-item.data-v-1887afd2 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 2rpx solid #f0f0f0;
}
.order-item.data-v-1887afd2:last-child {
  border-bottom: none;
}
.order-item .order-info.data-v-1887afd2 {
  flex: 1;
}
.order-item .order-info .order-title.data-v-1887afd2 {
  font-size: 28rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.order-item .order-info .order-distance.data-v-1887afd2 {
  font-size: 24rpx;
  color: #999;
}
.order-item .order-price.data-v-1887afd2 {
  font-size: 32rpx;
  font-weight: bold;
  color: #ff4d4f;
}
.empty-tip.data-v-1887afd2 {
  text-align: center;
  padding: 60rpx 0;
  color: #999;
  font-size: 28rpx;
}
.action-buttons.data-v-1887afd2 {
  display: flex;
  gap: 30rpx;
}
.action-buttons .action-btn.data-v-1887afd2 {
  flex: 1;
  height: 88rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  border: none;
}
.action-buttons .action-btn.primary.data-v-1887afd2 {
  background-color: #1890ff;
  color: #fff;
}
.action-buttons .action-btn.secondary.data-v-1887afd2 {
  background-color: #f5f5f5;
  color: #666;
}

