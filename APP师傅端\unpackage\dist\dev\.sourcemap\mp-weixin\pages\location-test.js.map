{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-test.vue?2910", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-test.vue?6ade", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-test.vue?42cd", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-test.vue?c08c", "uni-app:///pages/location-test.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-test.vue?0b14", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-test.vue?1126"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "permissionStatus", "hasPermission", "platform", "locationInfo", "logs", "onLoad", "methods", "addLog", "clearLogs", "checkPermission", "locationPermissionManager", "status", "requestPermission", "granted", "getLocation", "uni", "title", "locationManager", "forceUpdate", "silent", "locationData", "icon", "showPermissionDialog", "content", "userConfirmed", "openSettings"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuDh3B;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;IACA;MACAC;QACAC;QACAC;MACA;MACAC;MACAC;IACA;EACA;EAEAC;IACA;IACA;EACA;EAEAC;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACAC;cAAA;gBAAAC;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACAF;cAAA;gBAAAG;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBACAC;kBAAAC;gBAAA;gBAAA;gBAAA,OAEAC;kBACAC;kBACAC;gBACA;cAAA;gBAHAC;gBAKA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACAL;kBACAC;kBACAK;gBACA;cAAA;gBAAA;gBAEAN;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAO;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEA;gBAAA;gBAAA,OACAZ;kBACAM;kBACAO;gBACA;cAAA;gBAHAC;gBAIA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;QACAf;QACA;MACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACtKA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/location-test.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/location-test.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./location-test.vue?vue&type=template&id=bdc3f1fe&scoped=true&\"\nvar renderjs\nimport script from \"./location-test.vue?vue&type=script&lang=js&\"\nexport * from \"./location-test.vue?vue&type=script&lang=js&\"\nimport style0 from \"./location-test.vue?vue&type=style&index=0&id=bdc3f1fe&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"bdc3f1fe\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/location-test.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-test.vue?vue&type=template&id=bdc3f1fe&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-test.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-test.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"container\">\n    <view class=\"header\">\n      <text class=\"title\">定位权限测试页面</text>\n    </view>\n    \n    <view class=\"content\">\n      <!-- 权限状态显示 -->\n      <view class=\"status-card\">\n        <view class=\"status-title\">当前权限状态</view>\n        <view class=\"status-content\">\n          <text class=\"status-text\" :class=\"permissionStatus.hasPermission ? 'success' : 'error'\">\n            {{ permissionStatus.hasPermission ? '已开启定位权限' : '未开启定位权限' }}\n          </text>\n          <text class=\"platform-text\">平台：{{ permissionStatus.platform || '未知' }}</text>\n        </view>\n      </view>\n\n      <!-- 定位信息显示 -->\n      <view class=\"location-card\" v-if=\"locationInfo\">\n        <view class=\"location-title\">当前位置信息</view>\n        <view class=\"location-content\">\n          <text class=\"location-text\">地址：{{ locationInfo.address || '未获取' }}</text>\n          <text class=\"location-text\">省份：{{ locationInfo.province || '未知' }}</text>\n          <text class=\"location-text\">城市：{{ locationInfo.city || '未知' }}</text>\n          <text class=\"location-text\">区县：{{ locationInfo.district || '未知' }}</text>\n          <text class=\"location-text\">经度：{{ locationInfo.lng || '未知' }}</text>\n          <text class=\"location-text\">纬度：{{ locationInfo.lat || '未知' }}</text>\n        </view>\n      </view>\n\n      <!-- 操作按钮 -->\n      <view class=\"button-group\">\n        <button class=\"btn primary\" @click=\"checkPermission\">检查权限状态</button>\n        <button class=\"btn secondary\" @click=\"requestPermission\">请求权限</button>\n        <button class=\"btn success\" @click=\"getLocation\">获取定位</button>\n        <button class=\"btn warning\" @click=\"showPermissionDialog\">显示权限弹窗</button>\n        <button class=\"btn info\" @click=\"openSettings\">打开系统设置</button>\n      </view>\n\n      <!-- 日志显示 -->\n      <view class=\"log-card\">\n        <view class=\"log-title\">操作日志</view>\n        <scroll-view class=\"log-content\" scroll-y=\"true\">\n          <text class=\"log-item\" v-for=\"(log, index) in logs\" :key=\"index\">\n            {{ log }}\n          </text>\n        </scroll-view>\n        <button class=\"btn small\" @click=\"clearLogs\">清空日志</button>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport locationManager from '@/utils/location-manager.js';\nimport locationPermissionManager from '@/utils/location-permission.js';\n\nexport default {\n  data() {\n    return {\n      permissionStatus: {\n        hasPermission: false,\n        platform: 'unknown'\n      },\n      locationInfo: null,\n      logs: []\n    };\n  },\n  \n  onLoad() {\n    this.addLog('页面加载完成');\n    this.checkPermission();\n  },\n\n  methods: {\n    // 添加日志\n    addLog(message) {\n      const timestamp = new Date().toLocaleTimeString();\n      this.logs.unshift(`[${timestamp}] ${message}`);\n      if (this.logs.length > 50) {\n        this.logs = this.logs.slice(0, 50);\n      }\n    },\n\n    // 清空日志\n    clearLogs() {\n      this.logs = [];\n      this.addLog('日志已清空');\n    },\n\n    // 检查权限状态\n    async checkPermission() {\n      try {\n        this.addLog('开始检查权限状态...');\n        const status = await locationPermissionManager.checkPermissionStatus();\n        this.permissionStatus = status;\n        this.addLog(`权限检查完成: ${JSON.stringify(status)}`);\n      } catch (error) {\n        this.addLog(`权限检查失败: ${error.message}`);\n      }\n    },\n\n    // 请求权限\n    async requestPermission() {\n      try {\n        this.addLog('开始请求权限...');\n        const granted = await locationPermissionManager.requestPermission();\n        this.addLog(`权限请求结果: ${granted ? '已授权' : '被拒绝'}`);\n        \n        // 重新检查权限状态\n        await this.checkPermission();\n      } catch (error) {\n        this.addLog(`权限请求失败: ${error.message}`);\n      }\n    },\n\n    // 获取定位\n    async getLocation() {\n      try {\n        this.addLog('开始获取定位...');\n        uni.showLoading({ title: '获取定位中...' });\n        \n        const locationData = await locationManager.getLocation({ \n          forceUpdate: true, \n          silent: false \n        });\n        \n        this.locationInfo = locationData;\n        this.addLog(`定位获取成功: ${locationData.address}`);\n      } catch (error) {\n        this.addLog(`定位获取失败: ${error.message}`);\n        uni.showToast({\n          title: '定位获取失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    // 显示权限弹窗\n    async showPermissionDialog() {\n      try {\n        this.addLog('显示权限引导弹窗...');\n        const userConfirmed = await locationPermissionManager.showPermissionGuideDialog({\n          title: '定位权限测试',\n          content: '这是一个测试弹窗，用于演示权限引导功能。'\n        });\n        this.addLog(`用户操作结果: ${userConfirmed ? '确认去设置' : '取消'}`);\n      } catch (error) {\n        this.addLog(`显示弹窗失败: ${error.message}`);\n      }\n    },\n\n    // 打开系统设置\n    openSettings() {\n      try {\n        this.addLog('尝试打开系统设置...');\n        locationPermissionManager.openSystemSettings();\n        this.addLog('已尝试打开系统设置页面');\n      } catch (error) {\n        this.addLog(`打开设置失败: ${error.message}`);\n      }\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.container {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n  padding: 20rpx;\n}\n\n.header {\n  text-align: center;\n  padding: 40rpx 0;\n  \n  .title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n  }\n}\n\n.content {\n  .status-card, .location-card, .log-card {\n    background-color: #fff;\n    border-radius: 16rpx;\n    padding: 30rpx;\n    margin-bottom: 30rpx;\n    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);\n  }\n\n  .status-title, .location-title, .log-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n  }\n\n  .status-content, .location-content {\n    .status-text, .location-text {\n      display: block;\n      font-size: 28rpx;\n      margin-bottom: 10rpx;\n      \n      &.success {\n        color: #52c41a;\n      }\n      \n      &.error {\n        color: #ff4d4f;\n      }\n    }\n    \n    .platform-text {\n      display: block;\n      font-size: 24rpx;\n      color: #666;\n      margin-top: 10rpx;\n    }\n  }\n\n  .button-group {\n    display: flex;\n    flex-direction: column;\n    gap: 20rpx;\n    margin-bottom: 30rpx;\n  }\n\n  .btn {\n    height: 88rpx;\n    border-radius: 12rpx;\n    font-size: 30rpx;\n    border: none;\n    \n    &.primary {\n      background-color: #1890ff;\n      color: #fff;\n    }\n    \n    &.secondary {\n      background-color: #6c757d;\n      color: #fff;\n    }\n    \n    &.success {\n      background-color: #52c41a;\n      color: #fff;\n    }\n    \n    &.warning {\n      background-color: #faad14;\n      color: #fff;\n    }\n    \n    &.info {\n      background-color: #13c2c2;\n      color: #fff;\n    }\n    \n    &.small {\n      height: 60rpx;\n      font-size: 24rpx;\n      margin-top: 20rpx;\n      background-color: #f0f0f0;\n      color: #666;\n    }\n  }\n\n  .log-content {\n    height: 400rpx;\n    border: 2rpx solid #f0f0f0;\n    border-radius: 8rpx;\n    padding: 20rpx;\n    \n    .log-item {\n      display: block;\n      font-size: 24rpx;\n      color: #666;\n      margin-bottom: 10rpx;\n      line-height: 1.4;\n    }\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-test.vue?vue&type=style&index=0&id=bdc3f1fe&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-test.vue?vue&type=style&index=0&id=bdc3f1fe&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755745105764\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}