<view class="container data-v-bdc3f1fe"><view class="header data-v-bdc3f1fe"><text class="title data-v-bdc3f1fe">定位权限测试页面</text></view><view class="content data-v-bdc3f1fe"><view class="status-card data-v-bdc3f1fe"><view class="status-title data-v-bdc3f1fe">当前权限状态</view><view class="status-content data-v-bdc3f1fe"><text class="{{['status-text','data-v-bdc3f1fe',permissionStatus.hasPermission?'success':'error']}}">{{''+(permissionStatus.hasPermission?'已开启定位权限':'未开启定位权限')+''}}</text><text class="platform-text data-v-bdc3f1fe">{{"平台："+(permissionStatus.platform||'未知')}}</text></view></view><block wx:if="{{locationInfo}}"><view class="location-card data-v-bdc3f1fe"><view class="location-title data-v-bdc3f1fe">当前位置信息</view><view class="location-content data-v-bdc3f1fe"><text class="location-text data-v-bdc3f1fe">{{"地址："+(locationInfo.address||'未获取')}}</text><text class="location-text data-v-bdc3f1fe">{{"省份："+(locationInfo.province||'未知')}}</text><text class="location-text data-v-bdc3f1fe">{{"城市："+(locationInfo.city||'未知')}}</text><text class="location-text data-v-bdc3f1fe">{{"区县："+(locationInfo.district||'未知')}}</text><text class="location-text data-v-bdc3f1fe">{{"经度："+(locationInfo.lng||'未知')}}</text><text class="location-text data-v-bdc3f1fe">{{"纬度："+(locationInfo.lat||'未知')}}</text></view></view></block><view class="button-group data-v-bdc3f1fe"><button data-event-opts="{{[['tap',[['checkPermission',['$event']]]]]}}" class="btn primary data-v-bdc3f1fe" bindtap="__e">检查权限状态</button><button data-event-opts="{{[['tap',[['requestPermission',['$event']]]]]}}" class="btn secondary data-v-bdc3f1fe" bindtap="__e">请求权限</button><button data-event-opts="{{[['tap',[['getLocation',['$event']]]]]}}" class="btn success data-v-bdc3f1fe" bindtap="__e">获取定位</button><button data-event-opts="{{[['tap',[['showPermissionDialog',['$event']]]]]}}" class="btn warning data-v-bdc3f1fe" bindtap="__e">显示权限弹窗</button><button data-event-opts="{{[['tap',[['openSettings',['$event']]]]]}}" class="btn info data-v-bdc3f1fe" bindtap="__e">打开系统设置</button></view><view class="log-card data-v-bdc3f1fe"><view class="log-title data-v-bdc3f1fe">操作日志</view><scroll-view class="log-content data-v-bdc3f1fe" scroll-y="true"><block wx:for="{{logs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><text class="log-item data-v-bdc3f1fe">{{''+log+''}}</text></block></scroll-view><button data-event-opts="{{[['tap',[['clearLogs',['$event']]]]]}}" class="btn small data-v-bdc3f1fe" bindtap="__e">清空日志</button></view></view></view>