{"version": 3, "sources": ["uni-app:///main.js", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-demo.vue?2490", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-demo.vue?1a3a", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-demo.vue?7bf9", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-demo.vue?3db6", "uni-app:///pages/location-demo.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-demo.vue?9b89", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/location-demo.vue?39ad"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "LocationPermissionGuard", "data", "currentLocation", "orderList", "title", "distance", "price", "methods", "onPermissionGranted", "console", "uni", "icon", "onPermissionDenied", "onPermissionCancelled", "onOpenSettings", "duration", "getCurrentLocation", "locationManager", "forceUpdate", "silent", "checkPermission", "locationData", "refreshLocation", "startReceiving", "viewProfile", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,qBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA41B,CAAgB,42BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuDh3B;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACAC;EACA;EAEAC;IACA;MACAC;MACAC,YACA;QAAAC;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA,GACA;QAAAF;QAAAC;QAAAC;MAAA;IAEA;EACA;EAEAC;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;kBACAN;kBACAO;gBACA;;gBAEA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACAH;MACAC;QACAN;QACAO;MACA;IACA;IAEA;IACAE;MACAJ;MACA;IACA;IAEA;IACAK;MACAL;MACAC;QACAN;QACAO;QACAI;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAEAN;kBAAAN;gBAAA;gBAAA;gBAAA,OAEAa;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAJAC;gBAMA;kBACA;kBACAZ;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;gBACAC;kBACAN;kBACAO;gBACA;cAAA;gBAAA;gBAEAD;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAY;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;IACAC;MACAb;QACAN;QACAO;MACA;IACA;IAEA;IACAa;MACAd;QACAe;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAmmD,CAAgB,ujDAAG,EAAC,C;;;;;;;;;;;ACAvnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/location-demo.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/location-demo.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./location-demo.vue?vue&type=template&id=1887afd2&scoped=true&\"\nvar renderjs\nimport script from \"./location-demo.vue?vue&type=script&lang=js&\"\nexport * from \"./location-demo.vue?vue&type=script&lang=js&\"\nimport style0 from \"./location-demo.vue?vue&type=style&index=0&id=1887afd2&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1887afd2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/location-demo.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-demo.vue?vue&type=template&id=1887afd2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.orderList.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-demo.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-demo.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"page\">\n    <!-- 使用定位权限守卫组件 -->\n    <location-permission-guard\n      :auto-check=\"true\"\n      :required=\"true\"\n      message=\"师傅端需要获取您的位置信息来推荐附近的订单，请开启定位权限。\"\n      @permission-granted=\"onPermissionGranted\"\n      @permission-denied=\"onPermissionDenied\"\n      @permission-cancelled=\"onPermissionCancelled\"\n      @open-settings=\"onOpenSettings\"\n    >\n      <!-- 有权限时显示的内容 -->\n      <view class=\"content\">\n        <view class=\"header\">\n          <text class=\"title\">师傅接单大厅</text>\n          <text class=\"subtitle\">定位权限已开启</text>\n        </view>\n\n        <!-- 当前位置信息 -->\n        <view class=\"location-card\">\n          <view class=\"card-title\">当前接单位置</view>\n          <view class=\"location-info\">\n            <text class=\"location-text\">{{ currentLocation || '获取位置中...' }}</text>\n            <button class=\"refresh-btn\" @click=\"refreshLocation\">刷新位置</button>\n          </view>\n        </view>\n\n        <!-- 订单列表 -->\n        <view class=\"order-list\">\n          <view class=\"card-title\">附近订单</view>\n          <view class=\"order-item\" v-for=\"(order, index) in orderList\" :key=\"index\">\n            <view class=\"order-info\">\n              <text class=\"order-title\">{{ order.title }}</text>\n              <text class=\"order-distance\">距离：{{ order.distance }}</text>\n            </view>\n            <view class=\"order-price\">¥{{ order.price }}</view>\n          </view>\n          <view class=\"empty-tip\" v-if=\"orderList.length === 0\">\n            <text>暂无附近订单</text>\n          </view>\n        </view>\n\n        <!-- 功能按钮 -->\n        <view class=\"action-buttons\">\n          <button class=\"action-btn primary\" @click=\"startReceiving\">开始接单</button>\n          <button class=\"action-btn secondary\" @click=\"viewProfile\">个人中心</button>\n        </view>\n      </view>\n    </location-permission-guard>\n  </view>\n</template>\n\n<script>\nimport LocationPermissionGuard from '@/components/location-permission-guard.vue';\nimport locationManager from '@/utils/location-manager.js';\n\nexport default {\n  components: {\n    LocationPermissionGuard\n  },\n  \n  data() {\n    return {\n      currentLocation: '',\n      orderList: [\n        { title: '家电维修', distance: '0.5km', price: '80' },\n        { title: '管道疏通', distance: '1.2km', price: '120' },\n        { title: '空调清洗', distance: '2.1km', price: '150' }\n      ]\n    };\n  },\n\n  methods: {\n    // 权限授权成功\n    async onPermissionGranted() {\n      console.log('定位权限已授权');\n      uni.showToast({\n        title: '定位权限已开启',\n        icon: 'success'\n      });\n      \n      // 获取当前位置\n      await this.getCurrentLocation();\n    },\n\n    // 权限被拒绝\n    onPermissionDenied() {\n      console.log('定位权限被拒绝');\n      uni.showToast({\n        title: '定位权限被拒绝',\n        icon: 'none'\n      });\n    },\n\n    // 用户取消权限申请\n    onPermissionCancelled() {\n      console.log('用户取消权限申请');\n      // 可以跳转到其他页面或显示替代内容\n    },\n\n    // 用户点击去设置\n    onOpenSettings() {\n      console.log('用户点击去设置');\n      uni.showToast({\n        title: '请在设置中开启定位权限',\n        icon: 'none',\n        duration: 3000\n      });\n    },\n\n    // 获取当前位置\n    async getCurrentLocation() {\n      try {\n        uni.showLoading({ title: '获取位置中...' });\n        \n        const locationData = await locationManager.getLocation({\n          forceUpdate: true,\n          silent: false,\n          checkPermission: false // 已经通过守卫检查过权限\n        });\n\n        if (locationData) {\n          this.currentLocation = `${locationData.province}${locationData.city}${locationData.district}`;\n          console.log('位置获取成功:', locationData);\n        }\n      } catch (error) {\n        console.error('位置获取失败:', error);\n        uni.showToast({\n          title: '位置获取失败',\n          icon: 'none'\n        });\n      } finally {\n        uni.hideLoading();\n      }\n    },\n\n    // 刷新位置\n    async refreshLocation() {\n      await this.getCurrentLocation();\n    },\n\n    // 开始接单\n    startReceiving() {\n      uni.showToast({\n        title: '开始接单模式',\n        icon: 'success'\n      });\n    },\n\n    // 查看个人中心\n    viewProfile() {\n      uni.navigateTo({\n        url: '/pages/mine'\n      });\n    }\n  }\n};\n</script>\n\n<style scoped lang=\"scss\">\n.page {\n  min-height: 100vh;\n  background-color: #f5f5f5;\n}\n\n.content {\n  padding: 20rpx;\n}\n\n.header {\n  text-align: center;\n  padding: 40rpx 0;\n  background-color: #fff;\n  border-radius: 16rpx;\n  margin-bottom: 30rpx;\n\n  .title {\n    font-size: 36rpx;\n    font-weight: bold;\n    color: #333;\n    display: block;\n    margin-bottom: 10rpx;\n  }\n\n  .subtitle {\n    font-size: 24rpx;\n    color: #52c41a;\n  }\n}\n\n.location-card, .order-list {\n  background-color: #fff;\n  border-radius: 16rpx;\n  padding: 30rpx;\n  margin-bottom: 30rpx;\n\n  .card-title {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #333;\n    margin-bottom: 20rpx;\n  }\n}\n\n.location-info {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n\n  .location-text {\n    font-size: 28rpx;\n    color: #666;\n    flex: 1;\n  }\n\n  .refresh-btn {\n    background-color: #1890ff;\n    color: #fff;\n    border: none;\n    border-radius: 8rpx;\n    padding: 10rpx 20rpx;\n    font-size: 24rpx;\n  }\n}\n\n.order-item {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  padding: 20rpx 0;\n  border-bottom: 2rpx solid #f0f0f0;\n\n  &:last-child {\n    border-bottom: none;\n  }\n\n  .order-info {\n    flex: 1;\n\n    .order-title {\n      font-size: 28rpx;\n      color: #333;\n      display: block;\n      margin-bottom: 8rpx;\n    }\n\n    .order-distance {\n      font-size: 24rpx;\n      color: #999;\n    }\n  }\n\n  .order-price {\n    font-size: 32rpx;\n    font-weight: bold;\n    color: #ff4d4f;\n  }\n}\n\n.empty-tip {\n  text-align: center;\n  padding: 60rpx 0;\n  color: #999;\n  font-size: 28rpx;\n}\n\n.action-buttons {\n  display: flex;\n  gap: 30rpx;\n\n  .action-btn {\n    flex: 1;\n    height: 88rpx;\n    border-radius: 12rpx;\n    font-size: 30rpx;\n    border: none;\n\n    &.primary {\n      background-color: #1890ff;\n      color: #fff;\n    }\n\n    &.secondary {\n      background-color: #f5f5f5;\n      color: #666;\n    }\n  }\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-demo.vue?vue&type=style&index=0&id=1887afd2&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./location-demo.vue?vue&type=style&index=0&id=1887afd2&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1755745105751\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}